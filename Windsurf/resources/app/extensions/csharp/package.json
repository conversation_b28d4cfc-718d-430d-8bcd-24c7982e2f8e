{"name": "csharp", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "0.10.x"}, "scripts": {"update-grammar": "node ../node_modules/vscode-grammar-updater/bin dotnet/csharp-tmLanguage grammars/csharp.tmLanguage ./syntaxes/csharp.tmLanguage.json"}, "categories": ["Programming Languages"], "contributes": {"configurationDefaults": {"[csharp]": {"editor.maxTokenizationLineLength": 2500}}, "languages": [{"id": "csharp", "extensions": [".cs", ".csx", ".cake"], "aliases": ["C#", "csharp"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "csharp", "scopeName": "source.cs", "path": "./syntaxes/csharp.tmLanguage.json", "tokenTypes": {"meta.interpolation": "other"}}], "snippets": [{"language": "csharp", "path": "./snippets/csharp.code-snippets"}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}