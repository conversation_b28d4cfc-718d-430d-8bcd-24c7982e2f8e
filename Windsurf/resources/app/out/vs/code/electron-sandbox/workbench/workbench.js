/*!--------------------------------------------------------
 * Copyright (C) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------*/(function(){const u=window.vscode,y=u.process;async function f(s,e){const i=await b();e?.beforeImport?.(i);const{enableDeveloperKeybindings:a,removeDeveloperKeybindingsAfterLoad:t,developerDeveloperKeybindingsDisposable:o,forceDisableShowDevtoolsOnError:c}=v(i,e);l(i);const d=new URL(`${h(i.appRoot,{isWindows:y.platform==="win32",scheme:"vscode-file",fallbackAuthority:"vscode-app"})}/out/`);globalThis._VSCODE_FILE_ROOT=d.toString(),m(i,d);try{const n=await import(new URL(`${s}.js`,d).href);return o&&t&&o(),{result:n,configuration:i}}catch(n){throw p(n,a&&!c),n}}async function b(){const s=setTimeout(()=>{console.error("[resolve window config] Could not resolve window configuration within 10 seconds, but will continue to wait...")},1e4);performance.mark("code/willWaitForWindowConfig");const e=await u.context.resolveConfiguration();return performance.mark("code/didWaitForWindowConfig"),clearTimeout(s),e}function v(s,e){const{forceEnableDeveloperKeybindings:i,disallowReloadKeybinding:a,removeDeveloperKeybindingsAfterLoad:t,forceDisableShowDevtoolsOnError:o}=typeof e?.configureDeveloperSettings=="function"?e.configureDeveloperSettings(s):{forceEnableDeveloperKeybindings:!1,disallowReloadKeybinding:!1,removeDeveloperKeybindingsAfterLoad:!1,forceDisableShowDevtoolsOnError:!1},d=!!(!!y.env.VSCODE_DEV||i);let n;return d&&(n=r(a)),{enableDeveloperKeybindings:d,removeDeveloperKeybindingsAfterLoad:t,developerDeveloperKeybindingsDisposable:n,forceDisableShowDevtoolsOnError:o}}function r(s){const e=u.ipcRenderer,i=function(d){return[d.ctrlKey?"ctrl-":"",d.metaKey?"meta-":"",d.altKey?"alt-":"",d.shiftKey?"shift-":"",d.keyCode].join("")},a=y.platform==="darwin"?"meta-alt-73":"ctrl-shift-73",t="123",o=y.platform==="darwin"?"meta-82":"ctrl-82";let c=function(d){const n=i(d);n===a||n===t?e.send("vscode:toggleDevTools"):n===o&&!s&&e.send("vscode:reloadWindow")};return window.addEventListener("keydown",c),function(){c&&(window.removeEventListener("keydown",c),c=void 0)}}function l(s){globalThis._VSCODE_NLS_MESSAGES=s.nls.messages,globalThis._VSCODE_NLS_LANGUAGE=s.nls.language;let e=s.nls.language||"en";e==="zh-tw"?e="zh-Hant":e==="zh-cn"&&(e="zh-Hans"),window.document.documentElement.setAttribute("lang",e)}function p(s,e){e&&u.ipcRenderer.send("vscode:openDevTools"),console.error(`[uncaught exception]: ${s}`),s&&typeof s!="string"&&s.stack&&console.error(s.stack)}function h(s,e){let i=s.replace(/\\/g,"/");i.length>0&&i.charAt(0)!=="/"&&(i=`/${i}`);let a;return e.isWindows&&i.startsWith("//")?a=encodeURI(`${e.scheme||"file"}:${i}`):a=encodeURI(`${e.scheme||"file"}://${e.fallbackAuthority||""}${i}`),a.replace(/#/g,"%23")}function m(s,e){if(Array.isArray(s.cssModules)&&s.cssModules.length>0){performance.mark("code/willAddCssLoader");const i=document.createElement("style");i.type="text/css",i.media="screen",i.id="vscode-css-loading",document.head.appendChild(i),globalThis._VSCODE_CSS_LOAD=function(d){i.textContent+=`@import url(${d});
`};const a={imports:{react:"../../../../../node_modules/preact/compat/dist/compat.mjs",preact:"../../../../../node_modules/preact/dist/preact.mjs","preact/hooks":"../../../../../node_modules/preact/hooks/dist/hooks.mjs","react-dom/client":"../../../../../node_modules/preact/compat/client.mjs","preact/compat":"../../../../../node_modules/preact/compat/dist/compat.mjs","react/jsx-runtime":"../../../../../node_modules/preact/jsx-runtime/dist/jsxRuntime.mjs","@bufbuild/protobuf":"../../../../../node_modules/@bufbuild/protobuf/dist/esm/index.js","jsonc-parser":"../../../../../node_modules/jsonc-parser/lib/esm/main.js"}};for(const d of s.cssModules){const n=new URL(d,e).href,w=`globalThis._VSCODE_CSS_LOAD('${n}');
`,B=new Blob([w],{type:"application/javascript"});a.imports[n]=URL.createObjectURL(B)}const t=window.trustedTypes?.createPolicy("vscode-bootstrapImportMap",{createScript(d){return d}}),o=JSON.stringify(a,void 0,2),c=document.createElement("script");c.type="importmap",c.setAttribute("nonce","0c6a828f1297"),c.textContent=t?.createScript(o)??o,document.head.appendChild(c),performance.mark("code/didAddCssLoader")}}globalThis.MonacoBootstrapWindow={load:f}})(),async function(){performance.mark("code/didStartRenderer");const u=window.MonacoBootstrapWindow,y=window.vscode;function f(r){performance.mark("code/willShowPartsSplash");let l=r.partsSplash;l&&(r.autoDetectHighContrast&&r.colorScheme.highContrast?(r.colorScheme.dark&&l.baseTheme!=="hc-black"||!r.colorScheme.dark&&l.baseTheme!=="hc-light")&&(l=void 0):r.autoDetectColorScheme&&(r.colorScheme.dark&&l.baseTheme!=="vs-dark"||!r.colorScheme.dark&&l.baseTheme!=="vs")&&(l=void 0)),l&&r.extensionDevelopmentPath&&(l.layoutInfo=void 0);let p,h,m;l?(p=l.baseTheme,h=l.colorInfo.editorBackground,m=l.colorInfo.foreground):r.autoDetectHighContrast&&r.colorScheme.highContrast?r.colorScheme.dark?(p="hc-black",h="#000000",m="#FFFFFF"):(p="hc-light",h="#FFFFFF",m="#000000"):r.autoDetectColorScheme&&(r.colorScheme.dark?(p="vs-dark",h="#1E1E1E",m="#CCCCCC"):(p="vs",h="#FFFFFF",m="#000000"));const s=document.createElement("style");if(s.className="initialShellColors",window.document.head.appendChild(s),s.textContent=`body {	background-color: ${h}; color: ${m}; margin: 0; padding: 0; }`,typeof l?.zoomLevel=="number"&&typeof y?.webFrame?.setZoomLevel=="function"&&y.webFrame.setZoomLevel(l.zoomLevel),l?.layoutInfo){const{layoutInfo:e,colorInfo:i}=l,a=document.createElement("div");if(a.id="monaco-parts-splash",a.className=p??"vs-dark",e.windowBorder&&i.windowBorder){const t=document.createElement("div");t.style.position="absolute",t.style.width="calc(100vw - 2px)",t.style.height="calc(100vh - 2px)",t.style.zIndex="1",t.style.border="1px solid var(--window-border-color)",t.style.setProperty("--window-border-color",i.windowBorder),e.windowBorderRadius&&(t.style.borderRadius=e.windowBorderRadius),a.appendChild(t)}if(e.auxiliarySideBarWidth=Math.min(e.auxiliarySideBarWidth,window.innerWidth-(e.activityBarWidth+e.editorPartMinWidth+e.sideBarWidth)),e.sideBarWidth=Math.min(e.sideBarWidth,window.innerWidth-(e.activityBarWidth+e.editorPartMinWidth+e.auxiliarySideBarWidth)),e.titleBarHeight>0){const t=document.createElement("div");if(t.style.position="absolute",t.style.width="100%",t.style.height=`${e.titleBarHeight}px`,t.style.left="0",t.style.top="0",t.style.backgroundColor=`${i.titleBarBackground}`,t.style["-webkit-app-region"]="drag",a.appendChild(t),i.titleBarBorder){const o=document.createElement("div");o.style.position="absolute",o.style.width="100%",o.style.height="1px",o.style.left="0",o.style.bottom="0",o.style.borderBottom=`1px solid ${i.titleBarBorder}`,t.appendChild(o)}}if(e.activityBarWidth>0){const t=document.createElement("div");if(t.style.position="absolute",t.style.width=`${e.activityBarWidth}px`,t.style.height=`calc(100% - ${e.titleBarHeight+e.statusBarHeight}px)`,t.style.top=`${e.titleBarHeight}px`,e.sideBarSide==="left"?t.style.left="0":t.style.right="0",t.style.backgroundColor=`${i.activityBarBackground}`,a.appendChild(t),i.activityBarBorder){const o=document.createElement("div");o.style.position="absolute",o.style.width="1px",o.style.height="100%",o.style.top="0",e.sideBarSide==="left"?(o.style.right="0",o.style.borderRight=`1px solid ${i.activityBarBorder}`):(o.style.left="0",o.style.borderLeft=`1px solid ${i.activityBarBorder}`),t.appendChild(o)}}if(r.workspace&&e.sideBarWidth>0){const t=document.createElement("div");if(t.style.position="absolute",t.style.width=`${e.sideBarWidth}px`,t.style.height=`calc(100% - ${e.titleBarHeight+e.statusBarHeight}px)`,t.style.top=`${e.titleBarHeight}px`,e.sideBarSide==="left"?t.style.left=`${e.activityBarWidth}px`:t.style.right=`${e.activityBarWidth}px`,t.style.backgroundColor=`${i.sideBarBackground}`,a.appendChild(t),i.sideBarBorder){const o=document.createElement("div");o.style.position="absolute",o.style.width="1px",o.style.height="100%",o.style.top="0",o.style.right="0",e.sideBarSide==="left"?o.style.borderRight=`1px solid ${i.sideBarBorder}`:(o.style.left="0",o.style.borderLeft=`1px solid ${i.sideBarBorder}`),t.appendChild(o)}}if(e.auxiliarySideBarWidth>0){const t=document.createElement("div");if(t.style.position="absolute",t.style.width=`${e.auxiliarySideBarWidth}px`,t.style.height=`calc(100% - ${e.titleBarHeight+e.statusBarHeight}px)`,t.style.top=`${e.titleBarHeight}px`,e.sideBarSide==="left"?t.style.right="0":t.style.left="0",t.style.backgroundColor=`${i.sideBarBackground}`,a.appendChild(t),i.sideBarBorder){const o=document.createElement("div");o.style.position="absolute",o.style.width="1px",o.style.height="100%",o.style.top="0",e.sideBarSide==="left"?(o.style.left="0",o.style.borderLeft=`1px solid ${i.sideBarBorder}`):(o.style.right="0",o.style.borderRight=`1px solid ${i.sideBarBorder}`),t.appendChild(o)}}if(e.statusBarHeight>0){const t=document.createElement("div");if(t.style.position="absolute",t.style.width="100%",t.style.height=`${e.statusBarHeight}px`,t.style.bottom="0",t.style.left="0",r.workspace&&i.statusBarBackground?t.style.backgroundColor=i.statusBarBackground:!r.workspace&&i.statusBarNoFolderBackground&&(t.style.backgroundColor=i.statusBarNoFolderBackground),a.appendChild(t),i.statusBarBorder){const o=document.createElement("div");o.style.position="absolute",o.style.width="100%",o.style.height="1px",o.style.top="0",o.style.borderTop=`1px solid ${i.statusBarBorder}`,t.appendChild(o)}}window.document.body.appendChild(a)}performance.mark("code/didShowPartsSplash")}const{result:b,configuration:v}=await u.load("vs/workbench/workbench.desktop.main",{configureDeveloperSettings:function(r){return{forceDisableShowDevtoolsOnError:typeof r.extensionTestsPath=="string"||r["enable-smoke-test-driver"]===!0,forceEnableDeveloperKeybindings:Array.isArray(r.extensionDevelopmentPath)&&r.extensionDevelopmentPath.length>0,removeDeveloperKeybindingsAfterLoad:!0}},beforeImport:function(r){f(r),Object.defineProperty(window,"vscodeWindowId",{get:()=>r.windowId}),window.requestIdleCallback(()=>{const l=document.createElement("canvas");l.getContext("2d")?.clearRect(0,0,l.width,l.height),l.remove()},{timeout:50}),performance.mark("code/willLoadWorkbenchMain")}});performance.mark("code/didLoadWorkbenchMain"),b.main(v)}();

//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/6cb10202a0877c8b8d36fff300b7791e33a4d3e7/core/vs/code/electron-sandbox/workbench/workbench.js.map
