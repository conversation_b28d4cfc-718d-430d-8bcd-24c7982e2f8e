/*!--------------------------------------------------------
 * Copyright (C) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------*/(function(){const l=window.vscode,d=l.process;async function p(t,e){const o=await u();e?.beforeImport?.(o);const{enableDeveloperKeybindings:r,removeDeveloperKeybindingsAfterLoad:a,developerDeveloperKeybindingsDisposable:c,forceDisableShowDevtoolsOnError:i}=m(o,e);b(o);const n=new URL(`${g(o.appRoot,{isWindows:d.platform==="win32",scheme:"vscode-file",fallbackAuthority:"vscode-app"})}/out/`);globalThis._VSCODE_FILE_ROOT=n.toString(),v(o,n);try{const s=await import(new URL(`${t}.js`,n).href);return c&&a&&c(),{result:s,configuration:o}}catch(s){throw w(s,r&&!i),s}}async function u(){const t=setTimeout(()=>{console.error("[resolve window config] Could not resolve window configuration within 10 seconds, but will continue to wait...")},1e4);performance.mark("code/willWaitForWindowConfig");const e=await l.context.resolveConfiguration();return performance.mark("code/didWaitForWindowConfig"),clearTimeout(t),e}function m(t,e){const{forceEnableDeveloperKeybindings:o,disallowReloadKeybinding:r,removeDeveloperKeybindingsAfterLoad:a,forceDisableShowDevtoolsOnError:c}=typeof e?.configureDeveloperSettings=="function"?e.configureDeveloperSettings(t):{forceEnableDeveloperKeybindings:!1,disallowReloadKeybinding:!1,removeDeveloperKeybindingsAfterLoad:!1,forceDisableShowDevtoolsOnError:!1},n=!!(!!d.env.VSCODE_DEV||o);let s;return n&&(s=f(r)),{enableDeveloperKeybindings:n,removeDeveloperKeybindingsAfterLoad:a,developerDeveloperKeybindingsDisposable:s,forceDisableShowDevtoolsOnError:c}}function f(t){const e=l.ipcRenderer,o=function(n){return[n.ctrlKey?"ctrl-":"",n.metaKey?"meta-":"",n.altKey?"alt-":"",n.shiftKey?"shift-":"",n.keyCode].join("")},r=d.platform==="darwin"?"meta-alt-73":"ctrl-shift-73",a="123",c=d.platform==="darwin"?"meta-82":"ctrl-82";let i=function(n){const s=o(n);s===r||s===a?e.send("vscode:toggleDevTools"):s===c&&!t&&e.send("vscode:reloadWindow")};return window.addEventListener("keydown",i),function(){i&&(window.removeEventListener("keydown",i),i=void 0)}}function b(t){globalThis._VSCODE_NLS_MESSAGES=t.nls.messages,globalThis._VSCODE_NLS_LANGUAGE=t.nls.language;let e=t.nls.language||"en";e==="zh-tw"?e="zh-Hant":e==="zh-cn"&&(e="zh-Hans"),window.document.documentElement.setAttribute("lang",e)}function w(t,e){e&&l.ipcRenderer.send("vscode:openDevTools"),console.error(`[uncaught exception]: ${t}`),t&&typeof t!="string"&&t.stack&&console.error(t.stack)}function g(t,e){let o=t.replace(/\\/g,"/");o.length>0&&o.charAt(0)!=="/"&&(o=`/${o}`);let r;return e.isWindows&&o.startsWith("//")?r=encodeURI(`${e.scheme||"file"}:${o}`):r=encodeURI(`${e.scheme||"file"}://${e.fallbackAuthority||""}${o}`),r.replace(/#/g,"%23")}function v(t,e){if(Array.isArray(t.cssModules)&&t.cssModules.length>0){performance.mark("code/willAddCssLoader");const o=document.createElement("style");o.type="text/css",o.media="screen",o.id="vscode-css-loading",document.head.appendChild(o),globalThis._VSCODE_CSS_LOAD=function(n){o.textContent+=`@import url(${n});
`};const r={imports:{react:"../../../../../node_modules/preact/compat/dist/compat.mjs",preact:"../../../../../node_modules/preact/dist/preact.mjs","preact/hooks":"../../../../../node_modules/preact/hooks/dist/hooks.mjs","react-dom/client":"../../../../../node_modules/preact/compat/client.mjs","preact/compat":"../../../../../node_modules/preact/compat/dist/compat.mjs","react/jsx-runtime":"../../../../../node_modules/preact/jsx-runtime/dist/jsxRuntime.mjs","@bufbuild/protobuf":"../../../../../node_modules/@bufbuild/protobuf/dist/esm/index.js","jsonc-parser":"../../../../../node_modules/jsonc-parser/lib/esm/main.js"}};for(const n of t.cssModules){const s=new URL(n,e).href,y=`globalThis._VSCODE_CSS_LOAD('${s}');
`,h=new Blob([y],{type:"application/javascript"});r.imports[s]=URL.createObjectURL(h)}const a=window.trustedTypes?.createPolicy("vscode-bootstrapImportMap",{createScript(n){return n}}),c=JSON.stringify(r,void 0,2),i=document.createElement("script");i.type="importmap",i.setAttribute("nonce","0c6a828f1297"),i.textContent=a?.createScript(c)??c,document.head.appendChild(i),performance.mark("code/didAddCssLoader")}}globalThis.MonacoBootstrapWindow={load:p}})(),async function(){const l=window.MonacoBootstrapWindow,{result:d,configuration:p}=await l.load("vs/code/electron-sandbox/processExplorer/processExplorerMain",{configureDeveloperSettings:function(){return{forceEnableDeveloperKeybindings:!0}}});d.startup(p)}();

//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/6cb10202a0877c8b8d36fff300b7791e33a4d3e7/core/vs/code/electron-sandbox/processExplorer/processExplorer.js.map
