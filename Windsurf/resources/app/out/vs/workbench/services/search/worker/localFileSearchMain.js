/*!--------------------------------------------------------
 * Copyright (C) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------*/var ee=function(e,t){return ee=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(r[i]=n[i])},ee(e,t)};export function __extends(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");ee(e,t);function r(){this.constructor=e}e.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}export var __assign=function(){return __assign=Object.assign||function(t){for(var r,n=1,i=arguments.length;n<i;n++){r=arguments[n];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(t[s]=r[s])}return t},__assign.apply(this,arguments)};export function __rest(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)t.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]]);return r}export function __decorate(e,t,r,n){var i=arguments.length,s=i<3?t:n===null?n=Object.getOwnPropertyDescriptor(t,r):n,l;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")s=Reflect.decorate(e,t,r,n);else for(var o=e.length-1;o>=0;o--)(l=e[o])&&(s=(i<3?l(s):i>3?l(t,r,s):l(t,r))||s);return i>3&&s&&Object.defineProperty(t,r,s),s}export function __param(e,t){return function(r,n){t(r,n,e)}}export function __esDecorate(e,t,r,n,i,s){function l(N){if(N!==void 0&&typeof N!="function")throw new TypeError("Function expected");return N}for(var o=n.kind,c=o==="getter"?"get":o==="setter"?"set":"value",a=!t&&e?n.static?e:e.prototype:null,u=t||(a?Object.getOwnPropertyDescriptor(a,n.name):{}),f,h=!1,m=r.length-1;m>=0;m--){var v={};for(var y in n)v[y]=y==="access"?{}:n[y];for(var y in n.access)v.access[y]=n.access[y];v.addInitializer=function(N){if(h)throw new TypeError("Cannot add initializers after decoration has completed");s.push(l(N||null))};var g=(0,r[m])(o==="accessor"?{get:u.get,set:u.set}:u[c],v);if(o==="accessor"){if(g===void 0)continue;if(g===null||typeof g!="object")throw new TypeError("Object expected");(f=l(g.get))&&(u.get=f),(f=l(g.set))&&(u.set=f),(f=l(g.init))&&i.unshift(f)}else(f=l(g))&&(o==="field"?i.unshift(f):u[c]=f)}a&&Object.defineProperty(a,n.name,u),h=!0}export function __runInitializers(e,t,r){for(var n=arguments.length>2,i=0;i<t.length;i++)r=n?t[i].call(e,r):t[i].call(e);return n?r:void 0}export function __propKey(e){return typeof e=="symbol"?e:"".concat(e)}export function __setFunctionName(e,t,r){return typeof t=="symbol"&&(t=t.description?"[".concat(t.description,"]"):""),Object.defineProperty(e,"name",{configurable:!0,value:r?"".concat(r," ",t):t})}export function __metadata(e,t){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(e,t)}export function __awaiter(e,t,r,n){function i(s){return s instanceof r?s:new r(function(l){l(s)})}return new(r||(r=Promise))(function(s,l){function o(u){try{a(n.next(u))}catch(f){l(f)}}function c(u){try{a(n.throw(u))}catch(f){l(f)}}function a(u){u.done?s(u.value):i(u.value).then(o,c)}a((n=n.apply(e,t||[])).next())})}export function __generator(e,t){var r={label:0,sent:function(){if(s[0]&1)throw s[1];return s[1]},trys:[],ops:[]},n,i,s,l;return l={next:o(0),throw:o(1),return:o(2)},typeof Symbol=="function"&&(l[Symbol.iterator]=function(){return this}),l;function o(a){return function(u){return c([a,u])}}function c(a){if(n)throw new TypeError("Generator is already executing.");for(;l&&(l=0,a[0]&&(r=0)),r;)try{if(n=1,i&&(s=a[0]&2?i.return:a[0]?i.throw||((s=i.return)&&s.call(i),0):i.next)&&!(s=s.call(i,a[1])).done)return s;switch(i=0,s&&(a=[a[0]&2,s.value]),a[0]){case 0:case 1:s=a;break;case 4:return r.label++,{value:a[1],done:!1};case 5:r.label++,i=a[1],a=[0];continue;case 7:a=r.ops.pop(),r.trys.pop();continue;default:if(s=r.trys,!(s=s.length>0&&s[s.length-1])&&(a[0]===6||a[0]===2)){r=0;continue}if(a[0]===3&&(!s||a[1]>s[0]&&a[1]<s[3])){r.label=a[1];break}if(a[0]===6&&r.label<s[1]){r.label=s[1],s=a;break}if(s&&r.label<s[2]){r.label=s[2],r.ops.push(a);break}s[2]&&r.ops.pop(),r.trys.pop();continue}a=t.call(e,r)}catch(u){a=[6,u],i=0}finally{n=s=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}}export var __createBinding=Object.create?function(e,t,r,n){n===void 0&&(n=r);var i=Object.getOwnPropertyDescriptor(t,r);(!i||("get"in i?!t.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,i)}:function(e,t,r,n){n===void 0&&(n=r),e[n]=t[r]};export function __exportStar(e,t){for(var r in e)r!=="default"&&!Object.prototype.hasOwnProperty.call(t,r)&&__createBinding(t,e,r)}export function __values(e){var t=typeof Symbol=="function"&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}export function __read(e,t){var r=typeof Symbol=="function"&&e[Symbol.iterator];if(!r)return e;var n=r.call(e),i,s=[],l;try{for(;(t===void 0||t-- >0)&&!(i=n.next()).done;)s.push(i.value)}catch(o){l={error:o}}finally{try{i&&!i.done&&(r=n.return)&&r.call(n)}finally{if(l)throw l.error}}return s}export function __spread(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(__read(arguments[t]));return e}export function __spreadArrays(){for(var e=0,t=0,r=arguments.length;t<r;t++)e+=arguments[t].length;for(var n=Array(e),i=0,t=0;t<r;t++)for(var s=arguments[t],l=0,o=s.length;l<o;l++,i++)n[i]=s[l];return n}export function __spreadArray(e,t,r){if(r||arguments.length===2)for(var n=0,i=t.length,s;n<i;n++)(s||!(n in t))&&(s||(s=Array.prototype.slice.call(t,0,n)),s[n]=t[n]);return e.concat(s||Array.prototype.slice.call(t))}export function __await(e){return this instanceof __await?(this.v=e,this):new __await(e)}export function __asyncGenerator(e,t,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=r.apply(e,t||[]),i,s=[];return i={},o("next"),o("throw"),o("return",l),i[Symbol.asyncIterator]=function(){return this},i;function l(m){return function(v){return Promise.resolve(v).then(m,f)}}function o(m,v){n[m]&&(i[m]=function(y){return new Promise(function(g,N){s.push([m,y,g,N])>1||c(m,y)})},v&&(i[m]=v(i[m])))}function c(m,v){try{a(n[m](v))}catch(y){h(s[0][3],y)}}function a(m){m.value instanceof __await?Promise.resolve(m.value.v).then(u,f):h(s[0][2],m)}function u(m){c("next",m)}function f(m){c("throw",m)}function h(m,v){m(v),s.shift(),s.length&&c(s[0][0],s[0][1])}}export function __asyncDelegator(e){var t,r;return t={},n("next"),n("throw",function(i){throw i}),n("return"),t[Symbol.iterator]=function(){return this},t;function n(i,s){t[i]=e[i]?function(l){return(r=!r)?{value:__await(e[i](l)),done:!1}:s?s(l):l}:s}}export function __asyncValues(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],r;return t?t.call(e):(e=typeof __values=="function"?__values(e):e[Symbol.iterator](),r={},n("next"),n("throw"),n("return"),r[Symbol.asyncIterator]=function(){return this},r);function n(s){r[s]=e[s]&&function(l){return new Promise(function(o,c){l=e[s](l),i(o,c,l.done,l.value)})}}function i(s,l,o,c){Promise.resolve(c).then(function(a){s({value:a,done:o})},l)}}export function __makeTemplateObject(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e}var Vt=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t};export function __importStar(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)r!=="default"&&Object.prototype.hasOwnProperty.call(e,r)&&__createBinding(t,e,r);return Vt(t,e),t}export function __importDefault(e){return e&&e.__esModule?e:{default:e}}export function __classPrivateFieldGet(e,t,r,n){if(r==="a"&&!n)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return r==="m"?n:r==="a"?n.call(e):n?n.value:t.get(e)}export function __classPrivateFieldSet(e,t,r,n,i){if(n==="m")throw new TypeError("Private method is not writable");if(n==="a"&&!i)throw new TypeError("Private accessor was defined without a setter");if(typeof t=="function"?e!==t||!i:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return n==="a"?i.call(e,r):i?i.value=r:t.set(e,r),r}export function __classPrivateFieldIn(e,t){if(t===null||typeof t!="object"&&typeof t!="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof e=="function"?t===e:e.has(t)}export function __addDisposableResource(e,t,r){if(t!=null){if(typeof t!="object"&&typeof t!="function")throw new TypeError("Object expected.");var n,i;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");n=t[Symbol.asyncDispose]}if(n===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");n=t[Symbol.dispose],r&&(i=n)}if(typeof n!="function")throw new TypeError("Object not disposable.");i&&(n=function(){try{i.call(this)}catch(s){return Promise.reject(s)}}),e.stack.push({value:t,dispose:n,async:r})}else r&&e.stack.push({async:!0});return t}var Qt=typeof SuppressedError=="function"?SuppressedError:function(e,t,r){var n=new Error(r);return n.name="SuppressedError",n.error=e,n.suppressed=t,n};export function __disposeResources(e){function t(n){e.error=e.hasError?new Qt(n,e.error,"An error was suppressed during disposal."):n,e.hasError=!0}function r(){for(;e.stack.length;){var n=e.stack.pop();try{var i=n.dispose&&n.dispose.call(n.value);if(n.async)return Promise.resolve(i).then(r,function(s){return t(s),r()})}catch(s){t(s)}}if(e.hasError)throw e.error}return r()}export default{__extends,__assign,__rest,__decorate,__param,__metadata,__awaiter,__generator,__createBinding,__exportStar,__values,__read,__spread,__spreadArrays,__spreadArray,__await,__asyncGenerator,__asyncDelegator,__asyncValues,__makeTemplateObject,__importStar,__importDefault,__classPrivateFieldGet,__classPrivateFieldSet,__classPrivateFieldIn,__addDisposableResource,__disposeResources};var Kt=class{constructor(){this.b=[],this.a=function(e){setTimeout(()=>{throw e.stack?ne.isErrorNoTelemetry(e)?new ne(e.message+`

`+e.stack):new Error(e.message+`

`+e.stack):e},0)}}addListener(e){return this.b.push(e),()=>{this.d(e)}}c(e){this.b.forEach(t=>{t(e)})}d(e){this.b.splice(this.b.indexOf(e),1)}setUnexpectedErrorHandler(e){this.a=e}getUnexpectedErrorHandler(){return this.a}onUnexpectedError(e){this.a(e),this.c(e)}onUnexpectedExternalError(e){this.a(e)}},Jt=new Kt;function S1(e){Zt(e)||Jt.onUnexpectedError(e)}function te(e){if(e instanceof Error){const{name:t,message:r,cause:n}=e,i=e.stacktrace||e.stack;return{$isError:!0,name:t,message:r,stack:i,noTelemetry:ne.isErrorNoTelemetry(e),cause:n?te(n):void 0,code:e.code}}return e}var re="Canceled";function Zt(e){return e instanceof Gt?!0:e instanceof Error&&e.name===re&&e.message===re}var Gt=class extends Error{constructor(){super(re),this.name=this.message}},ne=class Se extends Error{constructor(t){super(t),this.name="CodeExpectedError"}static fromError(t){if(t instanceof Se)return t;const r=new Se;return r.message=t.message,r.stack=t.stack,r}static isErrorNoTelemetry(t){return t.name==="CodeExpectedError"}},Te;function Yt(e,t){const r=Object.create(null);for(const n of e){const i=t(n);let s=r[i];s||(s=r[i]=[]),s.push(n)}return r}var L2=class{static{Te=Symbol.toStringTag}constructor(e,t){this.b=t,this.a=new Map,this[Te]="SetWithKey";for(const r of e)this.add(r)}get size(){return this.a.size}add(e){const t=this.b(e);return this.a.set(t,e),this}delete(e){return this.a.delete(this.b(e))}has(e){return this.a.has(this.b(e))}*entries(){for(const e of this.a.values())yield[e,e]}keys(){return this.values()}*values(){for(const e of this.a.values())yield e}clear(){this.a.clear()}forEach(e,t){this.a.forEach(r=>e.call(t,r,r,this))}[Symbol.iterator](){return this.values()}};function Xt(e,t){const r=this;let n=!1,i;return function(){if(n)return i;if(n=!0,t)try{i=e.apply(r,arguments)}finally{t()}else i=e.apply(r,arguments);return i}}function er(e,t,r=0,n=e.length){let i=r,s=n;for(;i<s;){const l=Math.floor((i+s)/2);t(e[l])?i=l+1:s=l}return i-1}var $2=class jt{static{this.assertInvariants=!1}constructor(t){this.e=t,this.c=0}findLastMonotonous(t){if(jt.assertInvariants){if(this.d){for(const n of this.e)if(this.d(n)&&!t(n))throw new Error("MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.")}this.d=t}const r=er(this.e,t,this.c);return this.c=r+1,r===-1?void 0:this.e[r]}},ie;(function(e){function t(s){return s<0}e.isLessThan=t;function r(s){return s<=0}e.isLessThanOrEqual=r;function n(s){return s>0}e.isGreaterThan=n;function i(s){return s===0}e.isNeitherLessOrGreaterThan=i,e.greaterThan=1,e.lessThan=-1,e.neitherLessOrGreaterThan=0})(ie||(ie={}));function tr(e,t){return(r,n)=>t(e(r),e(n))}var rr=(e,t)=>e-t,A2=class J1{static{this.empty=new J1(t=>{})}constructor(t){this.iterate=t}forEach(t){this.iterate(r=>(t(r),!0))}toArray(){const t=[];return this.iterate(r=>(t.push(r),!0)),t}filter(t){return new J1(r=>this.iterate(n=>t(n)?r(n):!0))}map(t){return new J1(r=>this.iterate(n=>r(t(n))))}some(t){let r=!1;return this.iterate(n=>(r=t(n),!r)),r}findFirst(t){let r;return this.iterate(n=>t(n)?(r=n,!1):!0),r}findLast(t){let r;return this.iterate(n=>(t(n)&&(r=n),!0)),r}findLastMaxBy(t){let r,n=!0;return this.iterate(i=>((n||ie.isGreaterThan(t(i,r)))&&(n=!1,r=i),!0)),r}},Ie,De,Me,nr=class{constructor(e,t){this.uri=e,this.value=t}};function ir(e){return Array.isArray(e)}var je=class $1{static{this.c=t=>t.toString()}constructor(t,r){if(this[Ie]="ResourceMap",t instanceof $1)this.d=new Map(t.d),this.e=r??$1.c;else if(ir(t)){this.d=new Map,this.e=r??$1.c;for(const[n,i]of t)this.set(n,i)}else this.d=new Map,this.e=t??$1.c}set(t,r){return this.d.set(this.e(t),new nr(t,r)),this}get(t){return this.d.get(this.e(t))?.value}has(t){return this.d.has(this.e(t))}get size(){return this.d.size}clear(){this.d.clear()}delete(t){return this.d.delete(this.e(t))}forEach(t,r){typeof r<"u"&&(t=t.bind(r));for(const[n,i]of this.d)t(i.value,i.uri,this)}*values(){for(const t of this.d.values())yield t.value}*keys(){for(const t of this.d.values())yield t.uri}*entries(){for(const t of this.d.values())yield[t.uri,t.value]}*[(Ie=Symbol.toStringTag,Symbol.iterator)](){for(const[,t]of this.d)yield[t.uri,t.value]}},E2=class{constructor(e,t){this[De]="ResourceSet",!e||typeof e=="function"?this.c=new je(e):(this.c=new je(t),e.forEach(this.add,this))}get size(){return this.c.size}add(e){return this.c.set(e,e),this}clear(){this.c.clear()}delete(e){return this.c.delete(e)}forEach(e,t){this.c.forEach((r,n)=>e.call(t,n,n,this))}has(e){return this.c.has(e)}entries(){return this.c.entries()}keys(){return this.c.keys()}values(){return this.c.keys()}[(De=Symbol.toStringTag,Symbol.iterator)](){return this.keys()}},Ue;(function(e){e[e.None=0]="None",e[e.AsOld=1]="AsOld",e[e.AsNew=2]="AsNew"})(Ue||(Ue={}));var sr=class{constructor(){this[Me]="LinkedMap",this.c=new Map,this.d=void 0,this.e=void 0,this.f=0,this.g=0}clear(){this.c.clear(),this.d=void 0,this.e=void 0,this.f=0,this.g++}isEmpty(){return!this.d&&!this.e}get size(){return this.f}get first(){return this.d?.value}get last(){return this.e?.value}has(e){return this.c.has(e)}get(e,t=0){const r=this.c.get(e);if(r)return t!==0&&this.n(r,t),r.value}set(e,t,r=0){let n=this.c.get(e);if(n)n.value=t,r!==0&&this.n(n,r);else{switch(n={key:e,value:t,next:void 0,previous:void 0},r){case 0:this.l(n);break;case 1:this.k(n);break;case 2:this.l(n);break;default:this.l(n);break}this.c.set(e,n),this.f++}return this}delete(e){return!!this.remove(e)}remove(e){const t=this.c.get(e);if(t)return this.c.delete(e),this.m(t),this.f--,t.value}shift(){if(!this.d&&!this.e)return;if(!this.d||!this.e)throw new Error("Invalid list");const e=this.d;return this.c.delete(e.key),this.m(e),this.f--,e.value}forEach(e,t){const r=this.g;let n=this.d;for(;n;){if(t?e.bind(t)(n.value,n.key,this):e(n.value,n.key,this),this.g!==r)throw new Error("LinkedMap got modified during iteration.");n=n.next}}keys(){const e=this,t=this.g;let r=this.d;const n={[Symbol.iterator](){return n},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(r){const i={value:r.key,done:!1};return r=r.next,i}else return{value:void 0,done:!0}}};return n}values(){const e=this,t=this.g;let r=this.d;const n={[Symbol.iterator](){return n},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(r){const i={value:r.value,done:!1};return r=r.next,i}else return{value:void 0,done:!0}}};return n}entries(){const e=this,t=this.g;let r=this.d;const n={[Symbol.iterator](){return n},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(r){const i={value:[r.key,r.value],done:!1};return r=r.next,i}else return{value:void 0,done:!0}}};return n}[(Me=Symbol.toStringTag,Symbol.iterator)](){return this.entries()}h(e){if(e>=this.size)return;if(e===0){this.clear();return}let t=this.d,r=this.size;for(;t&&r>e;)this.c.delete(t.key),t=t.next,r--;this.d=t,this.f=r,t&&(t.previous=void 0),this.g++}j(e){if(e>=this.size)return;if(e===0){this.clear();return}let t=this.e,r=this.size;for(;t&&r>e;)this.c.delete(t.key),t=t.previous,r--;this.e=t,this.f=r,t&&(t.next=void 0),this.g++}k(e){if(!this.d&&!this.e)this.e=e;else if(this.d)e.next=this.d,this.d.previous=e;else throw new Error("Invalid list");this.d=e,this.g++}l(e){if(!this.d&&!this.e)this.d=e;else if(this.e)e.previous=this.e,this.e.next=e;else throw new Error("Invalid list");this.e=e,this.g++}m(e){if(e===this.d&&e===this.e)this.d=void 0,this.e=void 0;else if(e===this.d){if(!e.next)throw new Error("Invalid list");e.next.previous=void 0,this.d=e.next}else if(e===this.e){if(!e.previous)throw new Error("Invalid list");e.previous.next=void 0,this.e=e.previous}else{const t=e.next,r=e.previous;if(!t||!r)throw new Error("Invalid list");t.previous=r,r.next=t}e.next=void 0,e.previous=void 0,this.g++}n(e,t){if(!this.d||!this.e)throw new Error("Invalid list");if(!(t!==1&&t!==2)){if(t===1){if(e===this.d)return;const r=e.next,n=e.previous;e===this.e?(n.next=void 0,this.e=n):(r.previous=n,n.next=r),e.previous=void 0,e.next=this.d,this.d.previous=e,this.d=e,this.g++}else if(t===2){if(e===this.e)return;const r=e.next,n=e.previous;e===this.d?(r.previous=void 0,this.d=r):(r.previous=n,n.next=r),e.next=void 0,e.previous=this.e,this.e.next=e,this.e=e,this.g++}}}toJSON(){const e=[];return this.forEach((t,r)=>{e.push([r,t])}),e}fromJSON(e){this.clear();for(const[t,r]of e)this.set(t,r)}},or=class extends sr{constructor(e,t=1){super(),this.o=e,this.p=Math.min(Math.max(0,t),1)}get limit(){return this.o}set limit(e){this.o=e,this.q()}get ratio(){return this.p}set ratio(e){this.p=Math.min(Math.max(0,e),1),this.q()}get(e,t=2){return super.get(e,t)}peek(e){return super.get(e,0)}set(e,t){return super.set(e,t,2),this}q(){this.size>this.o&&this.r(Math.round(this.o*this.p))}},lr=class extends or{constructor(e,t=1){super(e,t)}r(e){this.h(e)}set(e,t){return super.set(e,t),this.q(),this}},ar=class{constructor(){this.c=new Map}add(e,t){let r=this.c.get(e);r||(r=new Set,this.c.set(e,r)),r.add(t)}delete(e,t){const r=this.c.get(e);r&&(r.delete(t),r.size===0&&this.c.delete(e))}forEach(e,t){const r=this.c.get(e);r&&r.forEach(t)}get(e){const t=this.c.get(e);return t||new Set}};function cr(e){return!!e&&typeof e[Symbol.iterator]=="function"}var se;(function(e){function t(C){return C&&typeof C=="object"&&typeof C[Symbol.iterator]=="function"}e.is=t;const r=Object.freeze([]);function n(){return r}e.empty=n;function*i(C){yield C}e.single=i;function s(C){return t(C)?C:i(C)}e.wrap=s;function l(C){return C||r}e.from=l;function*o(C){for(let $=C.length-1;$>=0;$--)yield C[$]}e.reverse=o;function c(C){return!C||C[Symbol.iterator]().next().done===!0}e.isEmpty=c;function a(C){return C[Symbol.iterator]().next().value}e.first=a;function u(C,$){let P=0;for(const O of C)if($(O,P++))return!0;return!1}e.some=u;function f(C,$){for(const P of C)if($(P))return P}e.find=f;function*h(C,$){for(const P of C)$(P)&&(yield P)}e.filter=h;function*m(C,$){let P=0;for(const O of C)yield $(O,P++)}e.map=m;function*v(C,$){let P=0;for(const O of C)yield*$(O,P++)}e.flatMap=v;function*y(...C){for(const $ of C)cr($)?yield*$:yield $}e.concat=y;function g(C,$,P){let O=P;for(const j of C)O=$(O,j);return O}e.reduce=g;function N(C){let $=0;for(const P of C)$++;return $}e.length=N;function*A(C,$,P=C.length){for($<-C.length&&($=0),$<0&&($+=C.length),P<0?P+=C.length:P>C.length&&(P=C.length);$<P;$++)yield C[$]}e.slice=A;function _(C,$=Number.POSITIVE_INFINITY){const P=[];if($===0)return[P,C];const O=C[Symbol.iterator]();for(let j=0;j<$;j++){const G=O.next();if(G.done)return[P,e.empty()];P.push(G.value)}return[P,{[Symbol.iterator](){return O}}]}e.consume=_;async function K(C){const $=[];for await(const P of C)$.push(P);return Promise.resolve($)}e.asyncToArray=K})(se||(se={}));var ur=!1,m1=null,x2=class Ut{constructor(){this.b=new Map}static{this.a=0}c(t){let r=this.b.get(t);return r||(r={parent:null,source:null,isSingleton:!1,value:t,idx:Ut.a++},this.b.set(t,r)),r}trackDisposable(t){const r=this.c(t);r.source||(r.source=new Error().stack)}setParent(t,r){const n=this.c(t);n.parent=r}markAsDisposed(t){this.b.delete(t)}markAsSingleton(t){this.c(t).isSingleton=!0}f(t,r){const n=r.get(t);if(n)return n;const i=t.parent?this.f(this.c(t.parent),r):t;return r.set(t,i),i}getTrackedDisposables(){const t=new Map;return[...this.b.entries()].filter(([,n])=>n.source!==null&&!this.f(n,t).isSingleton).flatMap(([n])=>n)}computeLeakingDisposables(t=10,r){let n;if(r)n=r;else{const c=new Map,a=[...this.b.values()].filter(f=>f.source!==null&&!this.f(f,c).isSingleton);if(a.length===0)return;const u=new Set(a.map(f=>f.value));if(n=a.filter(f=>!(f.parent&&u.has(f.parent))),n.length===0)throw new Error("There are cyclic diposable chains!")}if(!n)return;function i(c){function a(f,h){for(;f.length>0&&h.some(m=>typeof m=="string"?m===f[0]:f[0].match(m));)f.shift()}const u=c.source.split(`
`).map(f=>f.trim().replace("at ","")).filter(f=>f!=="");return a(u,["Error",/^trackDisposable \(.*\)$/,/^DisposableTracker.trackDisposable \(.*\)$/]),u.reverse()}const s=new ar;for(const c of n){const a=i(c);for(let u=0;u<=a.length;u++)s.add(a.slice(0,u).join(`
`),c)}n.sort(tr(c=>c.idx,rr));let l="",o=0;for(const c of n.slice(0,t)){o++;const a=i(c),u=[];for(let f=0;f<a.length;f++){let h=a[f];h=`(shared with ${s.get(a.slice(0,f+1).join(`
`)).size}/${n.length} leaks) at ${h}`;const v=s.get(a.slice(0,f).join(`
`)),y=Yt([...v].map(g=>i(g)[f]),g=>g);delete y[a[f]];for(const[g,N]of Object.entries(y))u.unshift(`    - stacktraces of ${N.length} other leaks continue with ${g}`);u.unshift(h)}l+=`


==================== Leaking disposable ${o}/${n.length}: ${c.value.constructor.name} ====================
${u.join(`
`)}
============================================================

`}return n.length>t&&(l+=`


... and ${n.length-t} more leaking disposables

`),{leaks:n,details:l}}};function fr(e){m1=e}if(ur){const e="__is_disposable_tracked__";fr(new class{trackDisposable(t){const r=new Error("Potentially leaked disposable").stack;setTimeout(()=>{t[e]||console.log(r)},3e3)}setParent(t,r){if(t&&t!==y1.None)try{t[e]=!0}catch{}}markAsDisposed(t){if(t&&t!==y1.None)try{t[e]=!0}catch{}}markAsSingleton(t){}})}function oe(e){return m1?.trackDisposable(e),e}function le(e){m1?.markAsDisposed(e)}function ae(e,t){m1?.setParent(e,t)}function hr(e,t){if(m1)for(const r of e)m1.setParent(r,t)}function qe(e){if(se.is(e)){const t=[];for(const r of e)if(r)try{r.dispose()}catch(n){t.push(n)}if(t.length===1)throw t[0];if(t.length>1)throw new AggregateError(t,"Encountered errors while disposing of store");return Array.isArray(e)?[]:e}else if(e)return e.dispose(),e}function dr(...e){const t=Fe(()=>qe(e));return hr(e,t),t}function Fe(e){const t=oe({dispose:Xt(()=>{le(t),e()})});return t}var ce=class qt{static{this.DISABLE_DISPOSED_WARNING=!1}constructor(){this.f=new Set,this.g=!1,oe(this)}dispose(){this.g||(le(this),this.g=!0,this.clear())}get isDisposed(){return this.g}clear(){if(this.f.size!==0)try{qe(this.f)}finally{this.f.clear()}}add(t){if(!t)return t;if(t===this)throw new Error("Cannot register a disposable on itself!");return ae(t,this),this.g?qt.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this.f.add(t),t}delete(t){if(t){if(t===this)throw new Error("Cannot dispose a disposable on itself!");this.f.delete(t),t.dispose()}}deleteAndLeak(t){t&&this.f.has(t)&&(this.f.delete(t),ae(t,null))}},y1=class{static{this.None=Object.freeze({dispose(){}})}constructor(){this.q=new ce,oe(this),ae(this.q,this)}dispose(){le(this),this.q.dispose()}B(e){if(e===this)throw new Error("Cannot register a disposable on itself!");return this.q.add(e)}},N2=class Z1{static{this.Undefined=new Z1(void 0)}constructor(t){this.element=t,this.next=Z1.Undefined,this.prev=Z1.Undefined}},mr=globalThis.performance&&typeof globalThis.performance.now=="function",vr=class Ft{static create(t){return new Ft(t)}constructor(t){this.c=mr&&t===!1?Date.now:globalThis.performance.now.bind(globalThis.performance),this.a=this.c(),this.b=-1}stop(){this.b=this.c()}reset(){this.a=this.c(),this.b=-1}elapsed(){return this.b!==-1?this.b-this.a:this.c()-this.a}},We=!1,gr=!1,k1;(function(e){e.None=()=>y1.None;function t(b){if(gr){const{onDidAddListener:d}=b,w=ue.create();let p=0;b.onDidAddListener=()=>{++p===2&&(console.warn("snapshotted emitter LIKELY used public and SHOULD HAVE BEEN created with DisposableStore. snapshotted here"),w.print()),d?.()}}}function r(b,d){return m(b,()=>{},0,void 0,!0,void 0,d)}e.defer=r;function n(b){return(d,w=null,p)=>{let L=!1,x;return x=b(R=>{if(!L)return x?x.dispose():L=!0,d.call(w,R)},null,p),L&&x.dispose(),x}}e.once=n;function i(b,d){return e.once(e.filter(b,d))}e.onceIf=i;function s(b,d,w){return f((p,L=null,x)=>b(R=>p.call(L,d(R)),null,x),w)}e.map=s;function l(b,d,w){return f((p,L=null,x)=>b(R=>{d(R),p.call(L,R)},null,x),w)}e.forEach=l;function o(b,d,w){return f((p,L=null,x)=>b(R=>d(R)&&p.call(L,R),null,x),w)}e.filter=o;function c(b){return b}e.signal=c;function a(...b){return(d,w=null,p)=>{const L=dr(...b.map(x=>x(R=>d.call(w,R))));return h(L,p)}}e.any=a;function u(b,d,w,p){let L=w;return s(b,x=>(L=d(L,x),L),p)}e.reduce=u;function f(b,d){let w;const p={onWillAddFirstListener(){w=b(L.fire,L)},onDidRemoveLastListener(){w?.dispose()}};d||t(p);const L=new Y(p);return d?.add(L),L.event}function h(b,d){return d instanceof Array?d.push(b):d&&d.add(b),b}function m(b,d,w=100,p=!1,L=!1,x,R){let U,z,d1,P1=0,b1;const Re={leakWarningThreshold:x,onWillAddFirstListener(){U=b(Bt=>{P1++,z=d(z,Bt),p&&!d1&&(_1.fire(z),z=void 0),b1=()=>{const Ht=z;z=void 0,d1=void 0,(!p||P1>1)&&_1.fire(Ht),P1=0},typeof w=="number"?(clearTimeout(d1),d1=setTimeout(b1,w)):d1===void 0&&(d1=0,queueMicrotask(b1))})},onWillRemoveListener(){L&&P1>0&&b1?.()},onDidRemoveLastListener(){b1=void 0,U.dispose()}};R||t(Re);const _1=new Y(Re);return R?.add(_1),_1.event}e.debounce=m;function v(b,d=0,w){return e.debounce(b,(p,L)=>p?(p.push(L),p):[L],d,void 0,!0,void 0,w)}e.accumulate=v;function y(b,d=(p,L)=>p===L,w){let p=!0,L;return o(b,x=>{const R=p||!d(x,L);return p=!1,L=x,R},w)}e.latch=y;function g(b,d,w){return[e.filter(b,d,w),e.filter(b,p=>!d(p),w)]}e.split=g;function N(b,d=!1,w=[],p){let L=w.slice(),x=b(z=>{L?L.push(z):U.fire(z)});p&&p.add(x);const R=()=>{L?.forEach(z=>U.fire(z)),L=null},U=new Y({onWillAddFirstListener(){x||(x=b(z=>U.fire(z)),p&&p.add(x))},onDidAddFirstListener(){L&&(d?setTimeout(R):R())},onDidRemoveLastListener(){x&&x.dispose(),x=null}});return p&&p.add(U),U.event}e.buffer=N;function A(b,d){return(p,L,x)=>{const R=d(new K);return b(function(U){const z=R.evaluate(U);z!==_&&p.call(L,z)},void 0,x)}}e.chain=A;const _=Symbol("HaltChainable");class K{constructor(){this.f=[]}map(d){return this.f.push(d),this}forEach(d){return this.f.push(w=>(d(w),w)),this}filter(d){return this.f.push(w=>d(w)?w:_),this}reduce(d,w){let p=w;return this.f.push(L=>(p=d(p,L),p)),this}latch(d=(w,p)=>w===p){let w=!0,p;return this.f.push(L=>{const x=w||!d(L,p);return w=!1,p=L,x?L:_}),this}evaluate(d){for(const w of this.f)if(d=w(d),d===_)break;return d}}function C(b,d,w=p=>p){const p=(...U)=>R.fire(w(...U)),L=()=>b.on(d,p),x=()=>b.removeListener(d,p),R=new Y({onWillAddFirstListener:L,onDidRemoveLastListener:x});return R.event}e.fromNodeEventEmitter=C;function $(b,d,w=p=>p){const p=(...U)=>R.fire(w(...U)),L=()=>b.addEventListener(d,p),x=()=>b.removeEventListener(d,p),R=new Y({onWillAddFirstListener:L,onDidRemoveLastListener:x});return R.event}e.fromDOMEventEmitter=$;function P(b,d){return new Promise(w=>n(b)(w,null,d))}e.toPromise=P;function O(b){const d=new Y;return b.then(w=>{d.fire(w)},()=>{d.fire(void 0)}).finally(()=>{d.dispose()}),d.event}e.fromPromise=O;function j(b,d){return b(w=>d.fire(w))}e.forward=j;function G(b,d,w){return d(w),b(p=>d(p))}e.runAndSubscribe=G;class c1{constructor(d,w){this._observable=d,this.f=0,this.g=!1;const p={onWillAddFirstListener:()=>{d.addObserver(this),this._observable.reportChanges()},onDidRemoveLastListener:()=>{d.removeObserver(this)}};w||t(p),this.emitter=new Y(p),w&&w.add(this.emitter)}beginUpdate(d){this.f++}handlePossibleChange(d){}handleChange(d,w){this.g=!0}endUpdate(d){this.f--,this.f===0&&(this._observable.reportChanges(),this.g&&(this.g=!1,this.emitter.fire(this._observable.get())))}}function N1(b,d){return new c1(b,d).emitter.event}e.fromObservable=N1;function X1(b){return(d,w,p)=>{let L=0,x=!1;const R={beginUpdate(){L++},endUpdate(){L--,L===0&&(b.reportChanges(),x&&(x=!1,d.call(w)))},handlePossibleChange(){},handleChange(){x=!0}};b.addObserver(R),b.reportChanges();const U={dispose(){b.removeObserver(R)}};return p instanceof ce?p.add(U):Array.isArray(p)&&p.push(U),U}}e.fromObservableLight=X1})(k1||(k1={}));var pr=class ke{static{this.all=new Set}static{this.f=0}constructor(t){this.listenerCount=0,this.invocationCount=0,this.elapsedOverall=0,this.durations=[],this.name=`${t}_${ke.f++}`,ke.all.add(this)}start(t){this.g=new vr,this.listenerCount=t}stop(){if(this.g){const t=this.g.elapsed();this.durations.push(t),this.elapsedOverall+=t,this.invocationCount+=1,this.g=void 0}}},ze=-1,wr=class Wt{static{this.f=1}constructor(t,r,n=(Wt.f++).toString(16).padStart(3,"0")){this.j=t,this.threshold=r,this.name=n,this.h=0}dispose(){this.g?.clear()}check(t,r){const n=this.threshold;if(n<=0||r<n)return;this.g||(this.g=new Map);const i=this.g.get(t.value)||0;if(this.g.set(t.value,i+1),this.h-=1,this.h<=0){this.h=n*.5;const[s,l]=this.getMostFrequentStack(),o=`[${this.name}] potential listener LEAK detected, having ${r} listeners already. MOST frequent listener (${l}):`;console.warn(o),console.warn(s);const c=new br(o,s);this.j(c)}return()=>{const s=this.g.get(t.value)||0;this.g.set(t.value,s-1)}}getMostFrequentStack(){if(!this.g)return;let t,r=0;for(const[n,i]of this.g)(!t||r<i)&&(t=[n,i],r=i);return t}},ue=class zt{static create(){const t=new Error;return new zt(t.stack??"")}constructor(t){this.value=t}print(){console.warn(this.value.split(`
`).slice(2).join(`
`))}},br=class extends Error{constructor(e,t){super(e),this.name="ListenerLeakError",this.stack=t}},yr=class extends Error{constructor(e,t){super(e),this.name="ListenerRefusalError",this.stack=t}},Cr=0,O1=class{constructor(e){this.value=e,this.id=Cr++}},Lr=2,$r=(e,t)=>{if(e instanceof O1)t(e);else for(let r=0;r<e.length;r++){const n=e[r];n&&t(n)}},Y=class{constructor(e){this.z=0,this.f=e,this.g=ze>0||this.f?.leakWarningThreshold?new wr(e?.onListenerError??S1,this.f?.leakWarningThreshold??ze):void 0,this.j=this.f?._profName?new pr(this.f._profName):void 0,this.w=this.f?.deliveryQueue}dispose(){if(!this.m){if(this.m=!0,this.w?.current===this&&this.w.reset(),this.u){if(We){const e=this.u;queueMicrotask(()=>{$r(e,t=>t.stack?.print())})}this.u=void 0,this.z=0}this.f?.onDidRemoveLastListener?.(),this.g?.dispose()}}get event(){return this.q??=(e,t,r)=>{if(this.g&&this.z>this.g.threshold**2){const o=`[${this.g.name}] REFUSES to accept new listeners because it exceeded its threshold by far (${this.z} vs ${this.g.threshold})`;console.warn(o);const c=this.g.getMostFrequentStack()??["UNKNOWN stack",-1],a=new yr(`${o}. HINT: Stack shows most frequent listener (${c[1]}-times)`,c[0]);return(this.f?.onListenerError||S1)(a),y1.None}if(this.m)return y1.None;t&&(e=e.bind(t));const n=new O1(e);let i,s;this.g&&this.z>=Math.ceil(this.g.threshold*.2)&&(n.stack=ue.create(),i=this.g.check(n.stack,this.z+1)),We&&(n.stack=s??ue.create()),this.u?this.u instanceof O1?(this.w??=new Ar,this.u=[this.u,n]):this.u.push(n):(this.f?.onWillAddFirstListener?.(this),this.u=n,this.f?.onDidAddFirstListener?.(this)),this.f?.onDidAddListener?.(this),this.z++;const l=Fe(()=>{i?.(),this.A(n)});return r instanceof ce?r.add(l):Array.isArray(r)&&r.push(l),l},this.q}A(e){if(this.f?.onWillRemoveListener?.(this),!this.u)return;if(this.z===1){this.u=void 0,this.f?.onDidRemoveLastListener?.(this),this.z=0;return}const t=this.u,r=t.indexOf(e);if(r===-1)throw console.log("disposed?",this.m),console.log("size?",this.z),console.log("arr?",JSON.stringify(this.u)),new Error("Attempted to dispose unknown listener");this.z--,t[r]=void 0;const n=this.w.current===this;if(this.z*Lr<=t.length){let i=0;for(let s=0;s<t.length;s++)t[s]?t[i++]=t[s]:n&&i<this.w.end&&(this.w.end--,i<this.w.i&&this.w.i--);t.length=i}}B(e,t){if(!e)return;const r=this.f?.onListenerError||S1;if(!r){e.value(t);return}try{e.value(t)}catch(n){r(n)}}C(e){const t=e.current.u;for(;e.i<e.end;)this.B(t[e.i++],e.value);e.reset()}fire(e){if(this.w?.current&&(this.C(this.w),this.j?.stop()),this.j?.start(this.z),this.u)if(this.u instanceof O1)this.B(this.u,e);else{const t=this.w;t.enqueue(this,e,this.u.length),this.C(t)}this.j?.stop()}hasListeners(){return this.z>0}},Ar=class{constructor(){this.i=-1,this.end=0}enqueue(e,t,r){this.i=0,this.end=r,this.current=e,this.value=t}reset(){this.i=this.end,this.current=void 0,this.value=void 0}};function Be(){return globalThis._VSCODE_NLS_LANGUAGE}var P2=Be()==="pseudo"||typeof document<"u"&&document.location&&typeof document.location.hash=="string"&&document.location.hash.indexOf("pseudo=true")>=0,v1="en",R1=!1,T1=!1,C1=!1,Er=!1,He=!1,fe=!1,xr=!1,Nr=!1,Pr=!1,_r=!1,I1=void 0,D1=v1,Ve=v1,Sr=void 0,t1=void 0,r1=globalThis,V=void 0;typeof r1.vscode<"u"&&typeof r1.vscode.process<"u"?V=r1.vscode.process:typeof process<"u"&&typeof process?.versions?.node=="string"&&(V=process);var Qe=typeof V?.versions?.electron=="string",kr=Qe&&V?.type==="renderer";if(typeof V=="object"){R1=V.platform==="win32",T1=V.platform==="darwin",C1=V.platform==="linux",Er=C1&&!!V.env.SNAP&&!!V.env.SNAP_REVISION,xr=Qe,Pr=!!V.env.CI||!!V.env.BUILD_ARTIFACTSTAGINGDIRECTORY,I1=v1,D1=v1;const e=V.env.VSCODE_NLS_CONFIG;if(e)try{const t=JSON.parse(e);I1=t.userLocale,Ve=t.osLocale,D1=t.resolvedLanguage||v1,Sr=t.languagePack?.translationsConfigFile}catch{}He=!0}else typeof navigator=="object"&&!kr?(t1=navigator.userAgent,R1=t1.indexOf("Windows")>=0,T1=t1.indexOf("Macintosh")>=0,Nr=(t1.indexOf("Macintosh")>=0||t1.indexOf("iPad")>=0||t1.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0,C1=t1.indexOf("Linux")>=0,_r=t1?.indexOf("Mobi")>=0,fe=!0,D1=Be()||v1,I1=navigator.language.toLowerCase(),Ve=I1):console.error("Unable to resolve platform.");var Ke;(function(e){e[e.Web=0]="Web",e[e.Mac=1]="Mac",e[e.Linux=2]="Linux",e[e.Windows=3]="Windows"})(Ke||(Ke={}));var he=0;T1?he=1:R1?he=3:C1&&(he=2);var g1=R1,Or=T1,Je=C1,Rr=He,Tr=fe,Ir=fe&&typeof r1.importScripts=="function",Dr=Ir?r1.origin:void 0,X=t1,o1=D1,Ze;(function(e){function t(){return o1}e.value=t;function r(){return o1.length===2?o1==="en":o1.length>=3?o1[0]==="e"&&o1[1]==="n"&&o1[2]==="-":!1}e.isDefaultVariant=r;function n(){return o1==="en"}e.isDefault=n})(Ze||(Ze={}));var Mr=typeof r1.postMessage=="function"&&!r1.importScripts,jr=(()=>{if(Mr){const e=[];r1.addEventListener("message",r=>{if(r.data&&r.data.vscodeScheduleAsyncWork)for(let n=0,i=e.length;n<i;n++){const s=e[n];if(s.id===r.data.vscodeScheduleAsyncWork){e.splice(n,1),s.callback();return}}});let t=0;return r=>{const n=++t;e.push({id:n,callback:r}),r1.postMessage({vscodeScheduleAsyncWork:n},"*")}}return e=>setTimeout(e)})(),Ge;(function(e){e[e.Windows=1]="Windows",e[e.Macintosh=2]="Macintosh",e[e.Linux=3]="Linux"})(Ge||(Ge={}));var Ur=!!(X&&X.indexOf("Chrome")>=0),_2=!!(X&&X.indexOf("Firefox")>=0),S2=!!(!Ur&&X&&X.indexOf("Safari")>=0),k2=!!(X&&X.indexOf("Edg/")>=0),O2=!!(X&&X.indexOf("Android")>=0),Ye=Object.freeze(function(e,t){const r=setTimeout(e.bind(t),0);return{dispose(){clearTimeout(r)}}}),M1;(function(e){function t(r){return r===e.None||r===e.Cancelled||r instanceof j1?!0:!r||typeof r!="object"?!1:typeof r.isCancellationRequested=="boolean"&&typeof r.onCancellationRequested=="function"}e.isCancellationToken=t,e.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:k1.None}),e.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:Ye})})(M1||(M1={}));var j1=class{constructor(){this.a=!1,this.b=null}cancel(){this.a||(this.a=!0,this.b&&(this.b.fire(void 0),this.dispose()))}get isCancellationRequested(){return this.a}get onCancellationRequested(){return this.a?Ye:(this.b||(this.b=new Y),this.b.event)}dispose(){this.b&&(this.b.dispose(),this.b=null)}},qr=class{constructor(e){this.f=void 0,this.g=void 0,this.g=e&&e.onCancellationRequested(this.cancel,this)}get token(){return this.f||(this.f=new j1),this.f}cancel(){this.f?this.f instanceof j1&&this.f.cancel():this.f=M1.Cancelled}dispose(e=!1){e&&this.cancel(),this.g?.dispose(),this.f?this.f instanceof j1&&this.f.dispose():this.f=M1.None}};function Fr(e){return e}var Wr=class{constructor(e,t){this.a=void 0,this.b=void 0,typeof e=="function"?(this.c=e,this.d=Fr):(this.c=t,this.d=e.getCacheKey)}get(e){const t=this.d(e);return this.b!==t&&(this.b=t,this.a=this.c(e)),this.a}},de=class{constructor(e){this.d=e,this.a=!1}get hasValue(){return this.a}get value(){if(!this.a)try{this.b=this.d()}catch(e){this.c=e}finally{this.a=!0}if(this.c)throw this.c;return this.b}get rawValue(){return this.b}};function me(e){return e.replace(/[\\\{\}\*\+\?\|\^\$\.\[\]\(\)]/g,"\\$&")}function zr(e,t){if(!e||!t)return e;const r=t.length;if(r===0||e.length===0)return e;let n=0;for(;e.indexOf(t,n)===n;)n=n+r;return e.substring(n)}function Br(e,t,r={}){if(!e)throw new Error("Cannot create regex from empty string");t||(e=me(e)),r.wholeWord&&(/\B/.test(e.charAt(0))||(e="\\b"+e),/\B/.test(e.charAt(e.length-1))||(e=e+"\\b"));let n="";return r.global&&(n+="g"),r.matchCase||(n+="i"),r.multiline&&(n+="m"),r.unicode&&(n+="u"),new RegExp(e,n)}function Hr(e,t){return e<t?-1:e>t?1:0}function Vr(e,t,r=0,n=e.length,i=0,s=t.length){for(;r<n&&i<s;r++,i++){const c=e.charCodeAt(r),a=t.charCodeAt(i);if(c<a)return-1;if(c>a)return 1}const l=n-r,o=s-i;return l<o?-1:l>o?1:0}function Xe(e,t,r=0,n=e.length,i=0,s=t.length){for(;r<n&&i<s;r++,i++){let c=e.charCodeAt(r),a=t.charCodeAt(i);if(c===a)continue;if(c>=128||a>=128)return Vr(e.toLowerCase(),t.toLowerCase(),r,n,i,s);et(c)&&(c-=32),et(a)&&(a-=32);const u=c-a;if(u!==0)return u}const l=n-r,o=s-i;return l<o?-1:l>o?1:0}function et(e){return e>=97&&e<=122}function tt(e){return e>=65&&e<=90}function Qr(e,t){return e.length===t.length&&Xe(e,t)===0}function Kr(e,t){const r=t.length;return t.length>e.length?!1:Xe(e,t,0,r)===0}var Jr=/(?:\x1b\[|\x9b)[=?>!]?[\d;:]*["$#'* ]?[a-zA-Z@^`{}|~]/,Zr=/(?:\x1b\]|\x9d).*?(?:\x1b\\|\x07|\x9c)/,Gr=/\x1b(?:[ #%\(\)\*\+\-\.\/]?[a-zA-Z0-9\|}~@])/,R2=new RegExp("(?:"+[Jr.source,Zr.source,Gr.source].join("|")+")","g"),T2="\uFEFF",rt;(function(e){e[e.Other=0]="Other",e[e.Prepend=1]="Prepend",e[e.CR=2]="CR",e[e.LF=3]="LF",e[e.Control=4]="Control",e[e.Extend=5]="Extend",e[e.Regional_Indicator=6]="Regional_Indicator",e[e.SpacingMark=7]="SpacingMark",e[e.L=8]="L",e[e.V=9]="V",e[e.T=10]="T",e[e.LV=11]="LV",e[e.LVT=12]="LVT",e[e.ZWJ=13]="ZWJ",e[e.Extended_Pictographic=14]="Extended_Pictographic"})(rt||(rt={}));var I2=class A1{static{this.c=null}static getInstance(){return A1.c||(A1.c=new A1),A1.c}constructor(){this.d=Yr()}getGraphemeBreakType(t){if(t<32)return t===10?3:t===13?2:4;if(t<127)return 0;const r=this.d,n=r.length/3;let i=1;for(;i<=n;)if(t<r[3*i])i=2*i;else if(t>r[3*i+1])i=2*i+1;else return r[3*i+2];return 0}};function Yr(){return JSON.parse("[0,0,0,51229,51255,12,44061,44087,12,127462,127487,6,7083,7085,5,47645,47671,12,54813,54839,12,128678,128678,14,3270,3270,5,9919,9923,14,45853,45879,12,49437,49463,12,53021,53047,12,71216,71218,7,128398,128399,14,129360,129374,14,2519,2519,5,4448,4519,9,9742,9742,14,12336,12336,14,44957,44983,12,46749,46775,12,48541,48567,12,50333,50359,12,52125,52151,12,53917,53943,12,69888,69890,5,73018,73018,5,127990,127990,14,128558,128559,14,128759,128760,14,129653,129655,14,2027,2035,5,2891,2892,7,3761,3761,5,6683,6683,5,8293,8293,4,9825,9826,14,9999,9999,14,43452,43453,5,44509,44535,12,45405,45431,12,46301,46327,12,47197,47223,12,48093,48119,12,48989,49015,12,49885,49911,12,50781,50807,12,51677,51703,12,52573,52599,12,53469,53495,12,54365,54391,12,65279,65279,4,70471,70472,7,72145,72147,7,119173,119179,5,127799,127818,14,128240,128244,14,128512,128512,14,128652,128652,14,128721,128722,14,129292,129292,14,129445,129450,14,129734,129743,14,1476,1477,5,2366,2368,7,2750,2752,7,3076,3076,5,3415,3415,5,4141,4144,5,6109,6109,5,6964,6964,5,7394,7400,5,9197,9198,14,9770,9770,14,9877,9877,14,9968,9969,14,10084,10084,14,43052,43052,5,43713,43713,5,44285,44311,12,44733,44759,12,45181,45207,12,45629,45655,12,46077,46103,12,46525,46551,12,46973,46999,12,47421,47447,12,47869,47895,12,48317,48343,12,48765,48791,12,49213,49239,12,49661,49687,12,50109,50135,12,50557,50583,12,51005,51031,12,51453,51479,12,51901,51927,12,52349,52375,12,52797,52823,12,53245,53271,12,53693,53719,12,54141,54167,12,54589,54615,12,55037,55063,12,69506,69509,5,70191,70193,5,70841,70841,7,71463,71467,5,72330,72342,5,94031,94031,5,123628,123631,5,127763,127765,14,127941,127941,14,128043,128062,14,128302,128317,14,128465,128467,14,128539,128539,14,128640,128640,14,128662,128662,14,128703,128703,14,128745,128745,14,129004,129007,14,129329,129330,14,129402,129402,14,129483,129483,14,129686,129704,14,130048,131069,14,173,173,4,1757,1757,1,2200,2207,5,2434,2435,7,2631,2632,5,2817,2817,5,3008,3008,5,3201,3201,5,3387,3388,5,3542,3542,5,3902,3903,7,4190,4192,5,6002,6003,5,6439,6440,5,6765,6770,7,7019,7027,5,7154,7155,7,8205,8205,13,8505,8505,14,9654,9654,14,9757,9757,14,9792,9792,14,9852,9853,14,9890,9894,14,9937,9937,14,9981,9981,14,10035,10036,14,11035,11036,14,42654,42655,5,43346,43347,7,43587,43587,5,44006,44007,7,44173,44199,12,44397,44423,12,44621,44647,12,44845,44871,12,45069,45095,12,45293,45319,12,45517,45543,12,45741,45767,12,45965,45991,12,46189,46215,12,46413,46439,12,46637,46663,12,46861,46887,12,47085,47111,12,47309,47335,12,47533,47559,12,47757,47783,12,47981,48007,12,48205,48231,12,48429,48455,12,48653,48679,12,48877,48903,12,49101,49127,12,49325,49351,12,49549,49575,12,49773,49799,12,49997,50023,12,50221,50247,12,50445,50471,12,50669,50695,12,50893,50919,12,51117,51143,12,51341,51367,12,51565,51591,12,51789,51815,12,52013,52039,12,52237,52263,12,52461,52487,12,52685,52711,12,52909,52935,12,53133,53159,12,53357,53383,12,53581,53607,12,53805,53831,12,54029,54055,12,54253,54279,12,54477,54503,12,54701,54727,12,54925,54951,12,55149,55175,12,68101,68102,5,69762,69762,7,70067,70069,7,70371,70378,5,70720,70721,7,71087,71087,5,71341,71341,5,71995,71996,5,72249,72249,7,72850,72871,5,73109,73109,5,118576,118598,5,121505,121519,5,127245,127247,14,127568,127569,14,127777,127777,14,127872,127891,14,127956,127967,14,128015,128016,14,128110,128172,14,128259,128259,14,128367,128368,14,128424,128424,14,128488,128488,14,128530,128532,14,128550,128551,14,128566,128566,14,128647,128647,14,128656,128656,14,128667,128673,14,128691,128693,14,128715,128715,14,128728,128732,14,128752,128752,14,128765,128767,14,129096,129103,14,129311,129311,14,129344,129349,14,129394,129394,14,129413,129425,14,129466,129471,14,129511,129535,14,129664,129666,14,129719,129722,14,129760,129767,14,917536,917631,5,13,13,2,1160,1161,5,1564,1564,4,1807,1807,1,2085,2087,5,2307,2307,7,2382,2383,7,2497,2500,5,2563,2563,7,2677,2677,5,2763,2764,7,2879,2879,5,2914,2915,5,3021,3021,5,3142,3144,5,3263,3263,5,3285,3286,5,3398,3400,7,3530,3530,5,3633,3633,5,3864,3865,5,3974,3975,5,4155,4156,7,4229,4230,5,5909,5909,7,6078,6085,7,6277,6278,5,6451,6456,7,6744,6750,5,6846,6846,5,6972,6972,5,7074,7077,5,7146,7148,7,7222,7223,5,7416,7417,5,8234,8238,4,8417,8417,5,9000,9000,14,9203,9203,14,9730,9731,14,9748,9749,14,9762,9763,14,9776,9783,14,9800,9811,14,9831,9831,14,9872,9873,14,9882,9882,14,9900,9903,14,9929,9933,14,9941,9960,14,9974,9974,14,9989,9989,14,10006,10006,14,10062,10062,14,10160,10160,14,11647,11647,5,12953,12953,14,43019,43019,5,43232,43249,5,43443,43443,5,43567,43568,7,43696,43696,5,43765,43765,7,44013,44013,5,44117,44143,12,44229,44255,12,44341,44367,12,44453,44479,12,44565,44591,12,44677,44703,12,44789,44815,12,44901,44927,12,45013,45039,12,45125,45151,12,45237,45263,12,45349,45375,12,45461,45487,12,45573,45599,12,45685,45711,12,45797,45823,12,45909,45935,12,46021,46047,12,46133,46159,12,46245,46271,12,46357,46383,12,46469,46495,12,46581,46607,12,46693,46719,12,46805,46831,12,46917,46943,12,47029,47055,12,47141,47167,12,47253,47279,12,47365,47391,12,47477,47503,12,47589,47615,12,47701,47727,12,47813,47839,12,47925,47951,12,48037,48063,12,48149,48175,12,48261,48287,12,48373,48399,12,48485,48511,12,48597,48623,12,48709,48735,12,48821,48847,12,48933,48959,12,49045,49071,12,49157,49183,12,49269,49295,12,49381,49407,12,49493,49519,12,49605,49631,12,49717,49743,12,49829,49855,12,49941,49967,12,50053,50079,12,50165,50191,12,50277,50303,12,50389,50415,12,50501,50527,12,50613,50639,12,50725,50751,12,50837,50863,12,50949,50975,12,51061,51087,12,51173,51199,12,51285,51311,12,51397,51423,12,51509,51535,12,51621,51647,12,51733,51759,12,51845,51871,12,51957,51983,12,52069,52095,12,52181,52207,12,52293,52319,12,52405,52431,12,52517,52543,12,52629,52655,12,52741,52767,12,52853,52879,12,52965,52991,12,53077,53103,12,53189,53215,12,53301,53327,12,53413,53439,12,53525,53551,12,53637,53663,12,53749,53775,12,53861,53887,12,53973,53999,12,54085,54111,12,54197,54223,12,54309,54335,12,54421,54447,12,54533,54559,12,54645,54671,12,54757,54783,12,54869,54895,12,54981,55007,12,55093,55119,12,55243,55291,10,66045,66045,5,68325,68326,5,69688,69702,5,69817,69818,5,69957,69958,7,70089,70092,5,70198,70199,5,70462,70462,5,70502,70508,5,70750,70750,5,70846,70846,7,71100,71101,5,71230,71230,7,71351,71351,5,71737,71738,5,72000,72000,7,72160,72160,5,72273,72278,5,72752,72758,5,72882,72883,5,73031,73031,5,73461,73462,7,94192,94193,7,119149,119149,7,121403,121452,5,122915,122916,5,126980,126980,14,127358,127359,14,127535,127535,14,127759,127759,14,127771,127771,14,127792,127793,14,127825,127867,14,127897,127899,14,127945,127945,14,127985,127986,14,128000,128007,14,128021,128021,14,128066,128100,14,128184,128235,14,128249,128252,14,128266,128276,14,128335,128335,14,128379,128390,14,128407,128419,14,128444,128444,14,128481,128481,14,128499,128499,14,128526,128526,14,128536,128536,14,128543,128543,14,128556,128556,14,128564,128564,14,128577,128580,14,128643,128645,14,128649,128649,14,128654,128654,14,128660,128660,14,128664,128664,14,128675,128675,14,128686,128689,14,128695,128696,14,128705,128709,14,128717,128719,14,128725,128725,14,128736,128741,14,128747,128748,14,128755,128755,14,128762,128762,14,128981,128991,14,129009,129023,14,129160,129167,14,129296,129304,14,129320,129327,14,129340,129342,14,129356,129356,14,129388,129392,14,129399,129400,14,129404,129407,14,129432,129442,14,129454,129455,14,129473,129474,14,129485,129487,14,129648,129651,14,129659,129660,14,129671,129679,14,129709,129711,14,129728,129730,14,129751,129753,14,129776,129782,14,917505,917505,4,917760,917999,5,10,10,3,127,159,4,768,879,5,1471,1471,5,1536,1541,1,1648,1648,5,1767,1768,5,1840,1866,5,2070,2073,5,2137,2139,5,2274,2274,1,2363,2363,7,2377,2380,7,2402,2403,5,2494,2494,5,2507,2508,7,2558,2558,5,2622,2624,7,2641,2641,5,2691,2691,7,2759,2760,5,2786,2787,5,2876,2876,5,2881,2884,5,2901,2902,5,3006,3006,5,3014,3016,7,3072,3072,5,3134,3136,5,3157,3158,5,3260,3260,5,3266,3266,5,3274,3275,7,3328,3329,5,3391,3392,7,3405,3405,5,3457,3457,5,3536,3537,7,3551,3551,5,3636,3642,5,3764,3772,5,3895,3895,5,3967,3967,7,3993,4028,5,4146,4151,5,4182,4183,7,4226,4226,5,4253,4253,5,4957,4959,5,5940,5940,7,6070,6070,7,6087,6088,7,6158,6158,4,6432,6434,5,6448,6449,7,6679,6680,5,6742,6742,5,6754,6754,5,6783,6783,5,6912,6915,5,6966,6970,5,6978,6978,5,7042,7042,7,7080,7081,5,7143,7143,7,7150,7150,7,7212,7219,5,7380,7392,5,7412,7412,5,8203,8203,4,8232,8232,4,8265,8265,14,8400,8412,5,8421,8432,5,8617,8618,14,9167,9167,14,9200,9200,14,9410,9410,14,9723,9726,14,9733,9733,14,9745,9745,14,9752,9752,14,9760,9760,14,9766,9766,14,9774,9774,14,9786,9786,14,9794,9794,14,9823,9823,14,9828,9828,14,9833,9850,14,9855,9855,14,9875,9875,14,9880,9880,14,9885,9887,14,9896,9897,14,9906,9916,14,9926,9927,14,9935,9935,14,9939,9939,14,9962,9962,14,9972,9972,14,9978,9978,14,9986,9986,14,9997,9997,14,10002,10002,14,10017,10017,14,10055,10055,14,10071,10071,14,10133,10135,14,10548,10549,14,11093,11093,14,12330,12333,5,12441,12442,5,42608,42610,5,43010,43010,5,43045,43046,5,43188,43203,7,43302,43309,5,43392,43394,5,43446,43449,5,43493,43493,5,43571,43572,7,43597,43597,7,43703,43704,5,43756,43757,5,44003,44004,7,44009,44010,7,44033,44059,12,44089,44115,12,44145,44171,12,44201,44227,12,44257,44283,12,44313,44339,12,44369,44395,12,44425,44451,12,44481,44507,12,44537,44563,12,44593,44619,12,44649,44675,12,44705,44731,12,44761,44787,12,44817,44843,12,44873,44899,12,44929,44955,12,44985,45011,12,45041,45067,12,45097,45123,12,45153,45179,12,45209,45235,12,45265,45291,12,45321,45347,12,45377,45403,12,45433,45459,12,45489,45515,12,45545,45571,12,45601,45627,12,45657,45683,12,45713,45739,12,45769,45795,12,45825,45851,12,45881,45907,12,45937,45963,12,45993,46019,12,46049,46075,12,46105,46131,12,46161,46187,12,46217,46243,12,46273,46299,12,46329,46355,12,46385,46411,12,46441,46467,12,46497,46523,12,46553,46579,12,46609,46635,12,46665,46691,12,46721,46747,12,46777,46803,12,46833,46859,12,46889,46915,12,46945,46971,12,47001,47027,12,47057,47083,12,47113,47139,12,47169,47195,12,47225,47251,12,47281,47307,12,47337,47363,12,47393,47419,12,47449,47475,12,47505,47531,12,47561,47587,12,47617,47643,12,47673,47699,12,47729,47755,12,47785,47811,12,47841,47867,12,47897,47923,12,47953,47979,12,48009,48035,12,48065,48091,12,48121,48147,12,48177,48203,12,48233,48259,12,48289,48315,12,48345,48371,12,48401,48427,12,48457,48483,12,48513,48539,12,48569,48595,12,48625,48651,12,48681,48707,12,48737,48763,12,48793,48819,12,48849,48875,12,48905,48931,12,48961,48987,12,49017,49043,12,49073,49099,12,49129,49155,12,49185,49211,12,49241,49267,12,49297,49323,12,49353,49379,12,49409,49435,12,49465,49491,12,49521,49547,12,49577,49603,12,49633,49659,12,49689,49715,12,49745,49771,12,49801,49827,12,49857,49883,12,49913,49939,12,49969,49995,12,50025,50051,12,50081,50107,12,50137,50163,12,50193,50219,12,50249,50275,12,50305,50331,12,50361,50387,12,50417,50443,12,50473,50499,12,50529,50555,12,50585,50611,12,50641,50667,12,50697,50723,12,50753,50779,12,50809,50835,12,50865,50891,12,50921,50947,12,50977,51003,12,51033,51059,12,51089,51115,12,51145,51171,12,51201,51227,12,51257,51283,12,51313,51339,12,51369,51395,12,51425,51451,12,51481,51507,12,51537,51563,12,51593,51619,12,51649,51675,12,51705,51731,12,51761,51787,12,51817,51843,12,51873,51899,12,51929,51955,12,51985,52011,12,52041,52067,12,52097,52123,12,52153,52179,12,52209,52235,12,52265,52291,12,52321,52347,12,52377,52403,12,52433,52459,12,52489,52515,12,52545,52571,12,52601,52627,12,52657,52683,12,52713,52739,12,52769,52795,12,52825,52851,12,52881,52907,12,52937,52963,12,52993,53019,12,53049,53075,12,53105,53131,12,53161,53187,12,53217,53243,12,53273,53299,12,53329,53355,12,53385,53411,12,53441,53467,12,53497,53523,12,53553,53579,12,53609,53635,12,53665,53691,12,53721,53747,12,53777,53803,12,53833,53859,12,53889,53915,12,53945,53971,12,54001,54027,12,54057,54083,12,54113,54139,12,54169,54195,12,54225,54251,12,54281,54307,12,54337,54363,12,54393,54419,12,54449,54475,12,54505,54531,12,54561,54587,12,54617,54643,12,54673,54699,12,54729,54755,12,54785,54811,12,54841,54867,12,54897,54923,12,54953,54979,12,55009,55035,12,55065,55091,12,55121,55147,12,55177,55203,12,65024,65039,5,65520,65528,4,66422,66426,5,68152,68154,5,69291,69292,5,69633,69633,5,69747,69748,5,69811,69814,5,69826,69826,5,69932,69932,7,70016,70017,5,70079,70080,7,70095,70095,5,70196,70196,5,70367,70367,5,70402,70403,7,70464,70464,5,70487,70487,5,70709,70711,7,70725,70725,7,70833,70834,7,70843,70844,7,70849,70849,7,71090,71093,5,71103,71104,5,71227,71228,7,71339,71339,5,71344,71349,5,71458,71461,5,71727,71735,5,71985,71989,7,71998,71998,5,72002,72002,7,72154,72155,5,72193,72202,5,72251,72254,5,72281,72283,5,72344,72345,5,72766,72766,7,72874,72880,5,72885,72886,5,73023,73029,5,73104,73105,5,73111,73111,5,92912,92916,5,94095,94098,5,113824,113827,4,119142,119142,7,119155,119162,4,119362,119364,5,121476,121476,5,122888,122904,5,123184,123190,5,125252,125258,5,127183,127183,14,127340,127343,14,127377,127386,14,127491,127503,14,127548,127551,14,127744,127756,14,127761,127761,14,127769,127769,14,127773,127774,14,127780,127788,14,127796,127797,14,127820,127823,14,127869,127869,14,127894,127895,14,127902,127903,14,127943,127943,14,127947,127950,14,127972,127972,14,127988,127988,14,127992,127994,14,128009,128011,14,128019,128019,14,128023,128041,14,128064,128064,14,128102,128107,14,128174,128181,14,128238,128238,14,128246,128247,14,128254,128254,14,128264,128264,14,128278,128299,14,128329,128330,14,128348,128359,14,128371,128377,14,128392,128393,14,128401,128404,14,128421,128421,14,128433,128434,14,128450,128452,14,128476,128478,14,128483,128483,14,128495,128495,14,128506,128506,14,128519,128520,14,128528,128528,14,128534,128534,14,128538,128538,14,128540,128542,14,128544,128549,14,128552,128555,14,128557,128557,14,128560,128563,14,128565,128565,14,128567,128576,14,128581,128591,14,128641,128642,14,128646,128646,14,128648,128648,14,128650,128651,14,128653,128653,14,128655,128655,14,128657,128659,14,128661,128661,14,128663,128663,14,128665,128666,14,128674,128674,14,128676,128677,14,128679,128685,14,128690,128690,14,128694,128694,14,128697,128702,14,128704,128704,14,128710,128714,14,128716,128716,14,128720,128720,14,128723,128724,14,128726,128727,14,128733,128735,14,128742,128744,14,128746,128746,14,128749,128751,14,128753,128754,14,128756,128758,14,128761,128761,14,128763,128764,14,128884,128895,14,128992,129003,14,129008,129008,14,129036,129039,14,129114,129119,14,129198,129279,14,129293,129295,14,129305,129310,14,129312,129319,14,129328,129328,14,129331,129338,14,129343,129343,14,129351,129355,14,129357,129359,14,129375,129387,14,129393,129393,14,129395,129398,14,129401,129401,14,129403,129403,14,129408,129412,14,129426,129431,14,129443,129444,14,129451,129453,14,129456,129465,14,129472,129472,14,129475,129482,14,129484,129484,14,129488,129510,14,129536,129647,14,129652,129652,14,129656,129658,14,129661,129663,14,129667,129670,14,129680,129685,14,129705,129708,14,129712,129718,14,129723,129727,14,129731,129733,14,129744,129750,14,129754,129759,14,129768,129775,14,129783,129791,14,917504,917504,4,917506,917535,4,917632,917759,4,918000,921599,4,0,9,4,11,12,4,14,31,4,169,169,14,174,174,14,1155,1159,5,1425,1469,5,1473,1474,5,1479,1479,5,1552,1562,5,1611,1631,5,1750,1756,5,1759,1764,5,1770,1773,5,1809,1809,5,1958,1968,5,2045,2045,5,2075,2083,5,2089,2093,5,2192,2193,1,2250,2273,5,2275,2306,5,2362,2362,5,2364,2364,5,2369,2376,5,2381,2381,5,2385,2391,5,2433,2433,5,2492,2492,5,2495,2496,7,2503,2504,7,2509,2509,5,2530,2531,5,2561,2562,5,2620,2620,5,2625,2626,5,2635,2637,5,2672,2673,5,2689,2690,5,2748,2748,5,2753,2757,5,2761,2761,7,2765,2765,5,2810,2815,5,2818,2819,7,2878,2878,5,2880,2880,7,2887,2888,7,2893,2893,5,2903,2903,5,2946,2946,5,3007,3007,7,3009,3010,7,3018,3020,7,3031,3031,5,3073,3075,7,3132,3132,5,3137,3140,7,3146,3149,5,3170,3171,5,3202,3203,7,3262,3262,7,3264,3265,7,3267,3268,7,3271,3272,7,3276,3277,5,3298,3299,5,3330,3331,7,3390,3390,5,3393,3396,5,3402,3404,7,3406,3406,1,3426,3427,5,3458,3459,7,3535,3535,5,3538,3540,5,3544,3550,7,3570,3571,7,3635,3635,7,3655,3662,5,3763,3763,7,3784,3789,5,3893,3893,5,3897,3897,5,3953,3966,5,3968,3972,5,3981,3991,5,4038,4038,5,4145,4145,7,4153,4154,5,4157,4158,5,4184,4185,5,4209,4212,5,4228,4228,7,4237,4237,5,4352,4447,8,4520,4607,10,5906,5908,5,5938,5939,5,5970,5971,5,6068,6069,5,6071,6077,5,6086,6086,5,6089,6099,5,6155,6157,5,6159,6159,5,6313,6313,5,6435,6438,7,6441,6443,7,6450,6450,5,6457,6459,5,6681,6682,7,6741,6741,7,6743,6743,7,6752,6752,5,6757,6764,5,6771,6780,5,6832,6845,5,6847,6862,5,6916,6916,7,6965,6965,5,6971,6971,7,6973,6977,7,6979,6980,7,7040,7041,5,7073,7073,7,7078,7079,7,7082,7082,7,7142,7142,5,7144,7145,5,7149,7149,5,7151,7153,5,7204,7211,7,7220,7221,7,7376,7378,5,7393,7393,7,7405,7405,5,7415,7415,7,7616,7679,5,8204,8204,5,8206,8207,4,8233,8233,4,8252,8252,14,8288,8292,4,8294,8303,4,8413,8416,5,8418,8420,5,8482,8482,14,8596,8601,14,8986,8987,14,9096,9096,14,9193,9196,14,9199,9199,14,9201,9202,14,9208,9210,14,9642,9643,14,9664,9664,14,9728,9729,14,9732,9732,14,9735,9741,14,9743,9744,14,9746,9746,14,9750,9751,14,9753,9756,14,9758,9759,14,9761,9761,14,9764,9765,14,9767,9769,14,9771,9773,14,9775,9775,14,9784,9785,14,9787,9791,14,9793,9793,14,9795,9799,14,9812,9822,14,9824,9824,14,9827,9827,14,9829,9830,14,9832,9832,14,9851,9851,14,9854,9854,14,9856,9861,14,9874,9874,14,9876,9876,14,9878,9879,14,9881,9881,14,9883,9884,14,9888,9889,14,9895,9895,14,9898,9899,14,9904,9905,14,9917,9918,14,9924,9925,14,9928,9928,14,9934,9934,14,9936,9936,14,9938,9938,14,9940,9940,14,9961,9961,14,9963,9967,14,9970,9971,14,9973,9973,14,9975,9977,14,9979,9980,14,9982,9985,14,9987,9988,14,9992,9996,14,9998,9998,14,10000,10001,14,10004,10004,14,10013,10013,14,10024,10024,14,10052,10052,14,10060,10060,14,10067,10069,14,10083,10083,14,10085,10087,14,10145,10145,14,10175,10175,14,11013,11015,14,11088,11088,14,11503,11505,5,11744,11775,5,12334,12335,5,12349,12349,14,12951,12951,14,42607,42607,5,42612,42621,5,42736,42737,5,43014,43014,5,43043,43044,7,43047,43047,7,43136,43137,7,43204,43205,5,43263,43263,5,43335,43345,5,43360,43388,8,43395,43395,7,43444,43445,7,43450,43451,7,43454,43456,7,43561,43566,5,43569,43570,5,43573,43574,5,43596,43596,5,43644,43644,5,43698,43700,5,43710,43711,5,43755,43755,7,43758,43759,7,43766,43766,5,44005,44005,5,44008,44008,5,44012,44012,7,44032,44032,11,44060,44060,11,44088,44088,11,44116,44116,11,44144,44144,11,44172,44172,11,44200,44200,11,44228,44228,11,44256,44256,11,44284,44284,11,44312,44312,11,44340,44340,11,44368,44368,11,44396,44396,11,44424,44424,11,44452,44452,11,44480,44480,11,44508,44508,11,44536,44536,11,44564,44564,11,44592,44592,11,44620,44620,11,44648,44648,11,44676,44676,11,44704,44704,11,44732,44732,11,44760,44760,11,44788,44788,11,44816,44816,11,44844,44844,11,44872,44872,11,44900,44900,11,44928,44928,11,44956,44956,11,44984,44984,11,45012,45012,11,45040,45040,11,45068,45068,11,45096,45096,11,45124,45124,11,45152,45152,11,45180,45180,11,45208,45208,11,45236,45236,11,45264,45264,11,45292,45292,11,45320,45320,11,45348,45348,11,45376,45376,11,45404,45404,11,45432,45432,11,45460,45460,11,45488,45488,11,45516,45516,11,45544,45544,11,45572,45572,11,45600,45600,11,45628,45628,11,45656,45656,11,45684,45684,11,45712,45712,11,45740,45740,11,45768,45768,11,45796,45796,11,45824,45824,11,45852,45852,11,45880,45880,11,45908,45908,11,45936,45936,11,45964,45964,11,45992,45992,11,46020,46020,11,46048,46048,11,46076,46076,11,46104,46104,11,46132,46132,11,46160,46160,11,46188,46188,11,46216,46216,11,46244,46244,11,46272,46272,11,46300,46300,11,46328,46328,11,46356,46356,11,46384,46384,11,46412,46412,11,46440,46440,11,46468,46468,11,46496,46496,11,46524,46524,11,46552,46552,11,46580,46580,11,46608,46608,11,46636,46636,11,46664,46664,11,46692,46692,11,46720,46720,11,46748,46748,11,46776,46776,11,46804,46804,11,46832,46832,11,46860,46860,11,46888,46888,11,46916,46916,11,46944,46944,11,46972,46972,11,47000,47000,11,47028,47028,11,47056,47056,11,47084,47084,11,47112,47112,11,47140,47140,11,47168,47168,11,47196,47196,11,47224,47224,11,47252,47252,11,47280,47280,11,47308,47308,11,47336,47336,11,47364,47364,11,47392,47392,11,47420,47420,11,47448,47448,11,47476,47476,11,47504,47504,11,47532,47532,11,47560,47560,11,47588,47588,11,47616,47616,11,47644,47644,11,47672,47672,11,47700,47700,11,47728,47728,11,47756,47756,11,47784,47784,11,47812,47812,11,47840,47840,11,47868,47868,11,47896,47896,11,47924,47924,11,47952,47952,11,47980,47980,11,48008,48008,11,48036,48036,11,48064,48064,11,48092,48092,11,48120,48120,11,48148,48148,11,48176,48176,11,48204,48204,11,48232,48232,11,48260,48260,11,48288,48288,11,48316,48316,11,48344,48344,11,48372,48372,11,48400,48400,11,48428,48428,11,48456,48456,11,48484,48484,11,48512,48512,11,48540,48540,11,48568,48568,11,48596,48596,11,48624,48624,11,48652,48652,11,48680,48680,11,48708,48708,11,48736,48736,11,48764,48764,11,48792,48792,11,48820,48820,11,48848,48848,11,48876,48876,11,48904,48904,11,48932,48932,11,48960,48960,11,48988,48988,11,49016,49016,11,49044,49044,11,49072,49072,11,49100,49100,11,49128,49128,11,49156,49156,11,49184,49184,11,49212,49212,11,49240,49240,11,49268,49268,11,49296,49296,11,49324,49324,11,49352,49352,11,49380,49380,11,49408,49408,11,49436,49436,11,49464,49464,11,49492,49492,11,49520,49520,11,49548,49548,11,49576,49576,11,49604,49604,11,49632,49632,11,49660,49660,11,49688,49688,11,49716,49716,11,49744,49744,11,49772,49772,11,49800,49800,11,49828,49828,11,49856,49856,11,49884,49884,11,49912,49912,11,49940,49940,11,49968,49968,11,49996,49996,11,50024,50024,11,50052,50052,11,50080,50080,11,50108,50108,11,50136,50136,11,50164,50164,11,50192,50192,11,50220,50220,11,50248,50248,11,50276,50276,11,50304,50304,11,50332,50332,11,50360,50360,11,50388,50388,11,50416,50416,11,50444,50444,11,50472,50472,11,50500,50500,11,50528,50528,11,50556,50556,11,50584,50584,11,50612,50612,11,50640,50640,11,50668,50668,11,50696,50696,11,50724,50724,11,50752,50752,11,50780,50780,11,50808,50808,11,50836,50836,11,50864,50864,11,50892,50892,11,50920,50920,11,50948,50948,11,50976,50976,11,51004,51004,11,51032,51032,11,51060,51060,11,51088,51088,11,51116,51116,11,51144,51144,11,51172,51172,11,51200,51200,11,51228,51228,11,51256,51256,11,51284,51284,11,51312,51312,11,51340,51340,11,51368,51368,11,51396,51396,11,51424,51424,11,51452,51452,11,51480,51480,11,51508,51508,11,51536,51536,11,51564,51564,11,51592,51592,11,51620,51620,11,51648,51648,11,51676,51676,11,51704,51704,11,51732,51732,11,51760,51760,11,51788,51788,11,51816,51816,11,51844,51844,11,51872,51872,11,51900,51900,11,51928,51928,11,51956,51956,11,51984,51984,11,52012,52012,11,52040,52040,11,52068,52068,11,52096,52096,11,52124,52124,11,52152,52152,11,52180,52180,11,52208,52208,11,52236,52236,11,52264,52264,11,52292,52292,11,52320,52320,11,52348,52348,11,52376,52376,11,52404,52404,11,52432,52432,11,52460,52460,11,52488,52488,11,52516,52516,11,52544,52544,11,52572,52572,11,52600,52600,11,52628,52628,11,52656,52656,11,52684,52684,11,52712,52712,11,52740,52740,11,52768,52768,11,52796,52796,11,52824,52824,11,52852,52852,11,52880,52880,11,52908,52908,11,52936,52936,11,52964,52964,11,52992,52992,11,53020,53020,11,53048,53048,11,53076,53076,11,53104,53104,11,53132,53132,11,53160,53160,11,53188,53188,11,53216,53216,11,53244,53244,11,53272,53272,11,53300,53300,11,53328,53328,11,53356,53356,11,53384,53384,11,53412,53412,11,53440,53440,11,53468,53468,11,53496,53496,11,53524,53524,11,53552,53552,11,53580,53580,11,53608,53608,11,53636,53636,11,53664,53664,11,53692,53692,11,53720,53720,11,53748,53748,11,53776,53776,11,53804,53804,11,53832,53832,11,53860,53860,11,53888,53888,11,53916,53916,11,53944,53944,11,53972,53972,11,54000,54000,11,54028,54028,11,54056,54056,11,54084,54084,11,54112,54112,11,54140,54140,11,54168,54168,11,54196,54196,11,54224,54224,11,54252,54252,11,54280,54280,11,54308,54308,11,54336,54336,11,54364,54364,11,54392,54392,11,54420,54420,11,54448,54448,11,54476,54476,11,54504,54504,11,54532,54532,11,54560,54560,11,54588,54588,11,54616,54616,11,54644,54644,11,54672,54672,11,54700,54700,11,54728,54728,11,54756,54756,11,54784,54784,11,54812,54812,11,54840,54840,11,54868,54868,11,54896,54896,11,54924,54924,11,54952,54952,11,54980,54980,11,55008,55008,11,55036,55036,11,55064,55064,11,55092,55092,11,55120,55120,11,55148,55148,11,55176,55176,11,55216,55238,9,64286,64286,5,65056,65071,5,65438,65439,5,65529,65531,4,66272,66272,5,68097,68099,5,68108,68111,5,68159,68159,5,68900,68903,5,69446,69456,5,69632,69632,7,69634,69634,7,69744,69744,5,69759,69761,5,69808,69810,7,69815,69816,7,69821,69821,1,69837,69837,1,69927,69931,5,69933,69940,5,70003,70003,5,70018,70018,7,70070,70078,5,70082,70083,1,70094,70094,7,70188,70190,7,70194,70195,7,70197,70197,7,70206,70206,5,70368,70370,7,70400,70401,5,70459,70460,5,70463,70463,7,70465,70468,7,70475,70477,7,70498,70499,7,70512,70516,5,70712,70719,5,70722,70724,5,70726,70726,5,70832,70832,5,70835,70840,5,70842,70842,5,70845,70845,5,70847,70848,5,70850,70851,5,71088,71089,7,71096,71099,7,71102,71102,7,71132,71133,5,71219,71226,5,71229,71229,5,71231,71232,5,71340,71340,7,71342,71343,7,71350,71350,7,71453,71455,5,71462,71462,7,71724,71726,7,71736,71736,7,71984,71984,5,71991,71992,7,71997,71997,7,71999,71999,1,72001,72001,1,72003,72003,5,72148,72151,5,72156,72159,7,72164,72164,7,72243,72248,5,72250,72250,1,72263,72263,5,72279,72280,7,72324,72329,1,72343,72343,7,72751,72751,7,72760,72765,5,72767,72767,5,72873,72873,7,72881,72881,7,72884,72884,7,73009,73014,5,73020,73021,5,73030,73030,1,73098,73102,7,73107,73108,7,73110,73110,7,73459,73460,5,78896,78904,4,92976,92982,5,94033,94087,7,94180,94180,5,113821,113822,5,118528,118573,5,119141,119141,5,119143,119145,5,119150,119154,5,119163,119170,5,119210,119213,5,121344,121398,5,121461,121461,5,121499,121503,5,122880,122886,5,122907,122913,5,122918,122922,5,123566,123566,5,125136,125142,5,126976,126979,14,126981,127182,14,127184,127231,14,127279,127279,14,127344,127345,14,127374,127374,14,127405,127461,14,127489,127490,14,127514,127514,14,127538,127546,14,127561,127567,14,127570,127743,14,127757,127758,14,127760,127760,14,127762,127762,14,127766,127768,14,127770,127770,14,127772,127772,14,127775,127776,14,127778,127779,14,127789,127791,14,127794,127795,14,127798,127798,14,127819,127819,14,127824,127824,14,127868,127868,14,127870,127871,14,127892,127893,14,127896,127896,14,127900,127901,14,127904,127940,14,127942,127942,14,127944,127944,14,127946,127946,14,127951,127955,14,127968,127971,14,127973,127984,14,127987,127987,14,127989,127989,14,127991,127991,14,127995,127999,5,128008,128008,14,128012,128014,14,128017,128018,14,128020,128020,14,128022,128022,14,128042,128042,14,128063,128063,14,128065,128065,14,128101,128101,14,128108,128109,14,128173,128173,14,128182,128183,14,128236,128237,14,128239,128239,14,128245,128245,14,128248,128248,14,128253,128253,14,128255,128258,14,128260,128263,14,128265,128265,14,128277,128277,14,128300,128301,14,128326,128328,14,128331,128334,14,128336,128347,14,128360,128366,14,128369,128370,14,128378,128378,14,128391,128391,14,128394,128397,14,128400,128400,14,128405,128406,14,128420,128420,14,128422,128423,14,128425,128432,14,128435,128443,14,128445,128449,14,128453,128464,14,128468,128475,14,128479,128480,14,128482,128482,14,128484,128487,14,128489,128494,14,128496,128498,14,128500,128505,14,128507,128511,14,128513,128518,14,128521,128525,14,128527,128527,14,128529,128529,14,128533,128533,14,128535,128535,14,128537,128537,14]")}var nt;(function(e){e[e.zwj=8205]="zwj",e[e.emojiVariantSelector=65039]="emojiVariantSelector",e[e.enclosingKeyCap=8419]="enclosingKeyCap",e[e.space=32]="space"})(nt||(nt={}));var D2=class E1{static{this.c=new de(()=>JSON.parse('{"_common":[8232,32,8233,32,5760,32,8192,32,8193,32,8194,32,8195,32,8196,32,8197,32,8198,32,8200,32,8201,32,8202,32,8287,32,8199,32,8239,32,2042,95,65101,95,65102,95,65103,95,8208,45,8209,45,8210,45,65112,45,1748,45,8259,45,727,45,8722,45,10134,45,11450,45,1549,44,1643,44,184,44,42233,44,894,59,2307,58,2691,58,1417,58,1795,58,1796,58,5868,58,65072,58,6147,58,6153,58,8282,58,1475,58,760,58,42889,58,8758,58,720,58,42237,58,451,33,11601,33,660,63,577,63,2429,63,5038,63,42731,63,119149,46,8228,46,1793,46,1794,46,42510,46,68176,46,1632,46,1776,46,42232,46,1373,96,65287,96,8219,96,1523,96,8242,96,1370,96,8175,96,65344,96,900,96,8189,96,8125,96,8127,96,8190,96,697,96,884,96,712,96,714,96,715,96,756,96,699,96,701,96,700,96,702,96,42892,96,1497,96,2036,96,2037,96,5194,96,5836,96,94033,96,94034,96,65339,91,10088,40,10098,40,12308,40,64830,40,65341,93,10089,41,10099,41,12309,41,64831,41,10100,123,119060,123,10101,125,65342,94,8270,42,1645,42,8727,42,66335,42,5941,47,8257,47,8725,47,8260,47,9585,47,10187,47,10744,47,119354,47,12755,47,12339,47,11462,47,20031,47,12035,47,65340,92,65128,92,8726,92,10189,92,10741,92,10745,92,119311,92,119355,92,12756,92,20022,92,12034,92,42872,38,708,94,710,94,5869,43,10133,43,66203,43,8249,60,10094,60,706,60,119350,60,5176,60,5810,60,5120,61,11840,61,12448,61,42239,61,8250,62,10095,62,707,62,119351,62,5171,62,94015,62,8275,126,732,126,8128,126,8764,126,65372,124,65293,45,118002,50,120784,50,120794,50,120804,50,120814,50,120824,50,130034,50,42842,50,423,50,1000,50,42564,50,5311,50,42735,50,119302,51,118003,51,120785,51,120795,51,120805,51,120815,51,120825,51,130035,51,42923,51,540,51,439,51,42858,51,11468,51,1248,51,94011,51,71882,51,118004,52,120786,52,120796,52,120806,52,120816,52,120826,52,130036,52,5070,52,71855,52,118005,53,120787,53,120797,53,120807,53,120817,53,120827,53,130037,53,444,53,71867,53,118006,54,120788,54,120798,54,120808,54,120818,54,120828,54,130038,54,11474,54,5102,54,71893,54,119314,55,118007,55,120789,55,120799,55,120809,55,120819,55,120829,55,130039,55,66770,55,71878,55,2819,56,2538,56,2666,56,125131,56,118008,56,120790,56,120800,56,120810,56,120820,56,120830,56,130040,56,547,56,546,56,66330,56,2663,57,2920,57,2541,57,3437,57,118009,57,120791,57,120801,57,120811,57,120821,57,120831,57,130041,57,42862,57,11466,57,71884,57,71852,57,71894,57,9082,97,65345,97,119834,97,119886,97,119938,97,119990,97,120042,97,120094,97,120146,97,120198,97,120250,97,120302,97,120354,97,120406,97,120458,97,593,97,945,97,120514,97,120572,97,120630,97,120688,97,120746,97,65313,65,117974,65,119808,65,119860,65,119912,65,119964,65,120016,65,120068,65,120120,65,120172,65,120224,65,120276,65,120328,65,120380,65,120432,65,913,65,120488,65,120546,65,120604,65,120662,65,120720,65,5034,65,5573,65,42222,65,94016,65,66208,65,119835,98,119887,98,119939,98,119991,98,120043,98,120095,98,120147,98,120199,98,120251,98,120303,98,120355,98,120407,98,120459,98,388,98,5071,98,5234,98,5551,98,65314,66,8492,66,117975,66,119809,66,119861,66,119913,66,120017,66,120069,66,120121,66,120173,66,120225,66,120277,66,120329,66,120381,66,120433,66,42932,66,914,66,120489,66,120547,66,120605,66,120663,66,120721,66,5108,66,5623,66,42192,66,66178,66,66209,66,66305,66,65347,99,8573,99,119836,99,119888,99,119940,99,119992,99,120044,99,120096,99,120148,99,120200,99,120252,99,120304,99,120356,99,120408,99,120460,99,7428,99,1010,99,11429,99,43951,99,66621,99,128844,67,71913,67,71922,67,65315,67,8557,67,8450,67,8493,67,117976,67,119810,67,119862,67,119914,67,119966,67,120018,67,120174,67,120226,67,120278,67,120330,67,120382,67,120434,67,1017,67,11428,67,5087,67,42202,67,66210,67,66306,67,66581,67,66844,67,8574,100,8518,100,119837,100,119889,100,119941,100,119993,100,120045,100,120097,100,120149,100,120201,100,120253,100,120305,100,120357,100,120409,100,120461,100,1281,100,5095,100,5231,100,42194,100,8558,68,8517,68,117977,68,119811,68,119863,68,119915,68,119967,68,120019,68,120071,68,120123,68,120175,68,120227,68,120279,68,120331,68,120383,68,120435,68,5024,68,5598,68,5610,68,42195,68,8494,101,65349,101,8495,101,8519,101,119838,101,119890,101,119942,101,120046,101,120098,101,120150,101,120202,101,120254,101,120306,101,120358,101,120410,101,120462,101,43826,101,1213,101,8959,69,65317,69,8496,69,117978,69,119812,69,119864,69,119916,69,120020,69,120072,69,120124,69,120176,69,120228,69,120280,69,120332,69,120384,69,120436,69,917,69,120492,69,120550,69,120608,69,120666,69,120724,69,11577,69,5036,69,42224,69,71846,69,71854,69,66182,69,119839,102,119891,102,119943,102,119995,102,120047,102,120099,102,120151,102,120203,102,120255,102,120307,102,120359,102,120411,102,120463,102,43829,102,42905,102,383,102,7837,102,1412,102,119315,70,8497,70,117979,70,119813,70,119865,70,119917,70,120021,70,120073,70,120125,70,120177,70,120229,70,120281,70,120333,70,120385,70,120437,70,42904,70,988,70,120778,70,5556,70,42205,70,71874,70,71842,70,66183,70,66213,70,66853,70,65351,103,8458,103,119840,103,119892,103,119944,103,120048,103,120100,103,120152,103,120204,103,120256,103,120308,103,120360,103,120412,103,120464,103,609,103,7555,103,397,103,1409,103,117980,71,119814,71,119866,71,119918,71,119970,71,120022,71,120074,71,120126,71,120178,71,120230,71,120282,71,120334,71,120386,71,120438,71,1292,71,5056,71,5107,71,42198,71,65352,104,8462,104,119841,104,119945,104,119997,104,120049,104,120101,104,120153,104,120205,104,120257,104,120309,104,120361,104,120413,104,120465,104,1211,104,1392,104,5058,104,65320,72,8459,72,8460,72,8461,72,117981,72,119815,72,119867,72,119919,72,120023,72,120179,72,120231,72,120283,72,120335,72,120387,72,120439,72,919,72,120494,72,120552,72,120610,72,120668,72,120726,72,11406,72,5051,72,5500,72,42215,72,66255,72,731,105,9075,105,65353,105,8560,105,8505,105,8520,105,119842,105,119894,105,119946,105,119998,105,120050,105,120102,105,120154,105,120206,105,120258,105,120310,105,120362,105,120414,105,120466,105,120484,105,618,105,617,105,953,105,8126,105,890,105,120522,105,120580,105,120638,105,120696,105,120754,105,1110,105,42567,105,1231,105,43893,105,5029,105,71875,105,65354,106,8521,106,119843,106,119895,106,119947,106,119999,106,120051,106,120103,106,120155,106,120207,106,120259,106,120311,106,120363,106,120415,106,120467,106,1011,106,1112,106,65322,74,117983,74,119817,74,119869,74,119921,74,119973,74,120025,74,120077,74,120129,74,120181,74,120233,74,120285,74,120337,74,120389,74,120441,74,42930,74,895,74,1032,74,5035,74,5261,74,42201,74,119844,107,119896,107,119948,107,120000,107,120052,107,120104,107,120156,107,120208,107,120260,107,120312,107,120364,107,120416,107,120468,107,8490,75,65323,75,117984,75,119818,75,119870,75,119922,75,119974,75,120026,75,120078,75,120130,75,120182,75,120234,75,120286,75,120338,75,120390,75,120442,75,922,75,120497,75,120555,75,120613,75,120671,75,120729,75,11412,75,5094,75,5845,75,42199,75,66840,75,1472,108,8739,73,9213,73,65512,73,1633,108,1777,73,66336,108,125127,108,118001,108,120783,73,120793,73,120803,73,120813,73,120823,73,130033,73,65321,73,8544,73,8464,73,8465,73,117982,108,119816,73,119868,73,119920,73,120024,73,120128,73,120180,73,120232,73,120284,73,120336,73,120388,73,120440,73,65356,108,8572,73,8467,108,119845,108,119897,108,119949,108,120001,108,120053,108,120105,73,120157,73,120209,73,120261,73,120313,73,120365,73,120417,73,120469,73,448,73,120496,73,120554,73,120612,73,120670,73,120728,73,11410,73,1030,73,1216,73,1493,108,1503,108,1575,108,126464,108,126592,108,65166,108,65165,108,1994,108,11599,73,5825,73,42226,73,93992,73,66186,124,66313,124,119338,76,8556,76,8466,76,117985,76,119819,76,119871,76,119923,76,120027,76,120079,76,120131,76,120183,76,120235,76,120287,76,120339,76,120391,76,120443,76,11472,76,5086,76,5290,76,42209,76,93974,76,71843,76,71858,76,66587,76,66854,76,65325,77,8559,77,8499,77,117986,77,119820,77,119872,77,119924,77,120028,77,120080,77,120132,77,120184,77,120236,77,120288,77,120340,77,120392,77,120444,77,924,77,120499,77,120557,77,120615,77,120673,77,120731,77,1018,77,11416,77,5047,77,5616,77,5846,77,42207,77,66224,77,66321,77,119847,110,119899,110,119951,110,120003,110,120055,110,120107,110,120159,110,120211,110,120263,110,120315,110,120367,110,120419,110,120471,110,1400,110,1404,110,65326,78,8469,78,117987,78,119821,78,119873,78,119925,78,119977,78,120029,78,120081,78,120185,78,120237,78,120289,78,120341,78,120393,78,120445,78,925,78,120500,78,120558,78,120616,78,120674,78,120732,78,11418,78,42208,78,66835,78,3074,111,3202,111,3330,111,3458,111,2406,111,2662,111,2790,111,3046,111,3174,111,3302,111,3430,111,3664,111,3792,111,4160,111,1637,111,1781,111,65359,111,8500,111,119848,111,119900,111,119952,111,120056,111,120108,111,120160,111,120212,111,120264,111,120316,111,120368,111,120420,111,120472,111,7439,111,7441,111,43837,111,959,111,120528,111,120586,111,120644,111,120702,111,120760,111,963,111,120532,111,120590,111,120648,111,120706,111,120764,111,11423,111,4351,111,1413,111,1505,111,1607,111,126500,111,126564,111,126596,111,65259,111,65260,111,65258,111,65257,111,1726,111,64428,111,64429,111,64427,111,64426,111,1729,111,64424,111,64425,111,64423,111,64422,111,1749,111,3360,111,4125,111,66794,111,71880,111,71895,111,66604,111,1984,79,2534,79,2918,79,12295,79,70864,79,71904,79,118000,79,120782,79,120792,79,120802,79,120812,79,120822,79,130032,79,65327,79,117988,79,119822,79,119874,79,119926,79,119978,79,120030,79,120082,79,120134,79,120186,79,120238,79,120290,79,120342,79,120394,79,120446,79,927,79,120502,79,120560,79,120618,79,120676,79,120734,79,11422,79,1365,79,11604,79,4816,79,2848,79,66754,79,42227,79,71861,79,66194,79,66219,79,66564,79,66838,79,9076,112,65360,112,119849,112,119901,112,119953,112,120005,112,120057,112,120109,112,120161,112,120213,112,120265,112,120317,112,120369,112,120421,112,120473,112,961,112,120530,112,120544,112,120588,112,120602,112,120646,112,120660,112,120704,112,120718,112,120762,112,120776,112,11427,112,65328,80,8473,80,117989,80,119823,80,119875,80,119927,80,119979,80,120031,80,120083,80,120187,80,120239,80,120291,80,120343,80,120395,80,120447,80,929,80,120504,80,120562,80,120620,80,120678,80,120736,80,11426,80,5090,80,5229,80,42193,80,66197,80,119850,113,119902,113,119954,113,120006,113,120058,113,120110,113,120162,113,120214,113,120266,113,120318,113,120370,113,120422,113,120474,113,1307,113,1379,113,1382,113,8474,81,117990,81,119824,81,119876,81,119928,81,119980,81,120032,81,120084,81,120188,81,120240,81,120292,81,120344,81,120396,81,120448,81,11605,81,119851,114,119903,114,119955,114,120007,114,120059,114,120111,114,120163,114,120215,114,120267,114,120319,114,120371,114,120423,114,120475,114,43847,114,43848,114,7462,114,11397,114,43905,114,119318,82,8475,82,8476,82,8477,82,117991,82,119825,82,119877,82,119929,82,120033,82,120189,82,120241,82,120293,82,120345,82,120397,82,120449,82,422,82,5025,82,5074,82,66740,82,5511,82,42211,82,94005,82,65363,115,119852,115,119904,115,119956,115,120008,115,120060,115,120112,115,120164,115,120216,115,120268,115,120320,115,120372,115,120424,115,120476,115,42801,115,445,115,1109,115,43946,115,71873,115,66632,115,65331,83,117992,83,119826,83,119878,83,119930,83,119982,83,120034,83,120086,83,120138,83,120190,83,120242,83,120294,83,120346,83,120398,83,120450,83,1029,83,1359,83,5077,83,5082,83,42210,83,94010,83,66198,83,66592,83,119853,116,119905,116,119957,116,120009,116,120061,116,120113,116,120165,116,120217,116,120269,116,120321,116,120373,116,120425,116,120477,116,8868,84,10201,84,128872,84,65332,84,117993,84,119827,84,119879,84,119931,84,119983,84,120035,84,120087,84,120139,84,120191,84,120243,84,120295,84,120347,84,120399,84,120451,84,932,84,120507,84,120565,84,120623,84,120681,84,120739,84,11430,84,5026,84,42196,84,93962,84,71868,84,66199,84,66225,84,66325,84,119854,117,119906,117,119958,117,120010,117,120062,117,120114,117,120166,117,120218,117,120270,117,120322,117,120374,117,120426,117,120478,117,42911,117,7452,117,43854,117,43858,117,651,117,965,117,120534,117,120592,117,120650,117,120708,117,120766,117,1405,117,66806,117,71896,117,8746,85,8899,85,117994,85,119828,85,119880,85,119932,85,119984,85,120036,85,120088,85,120140,85,120192,85,120244,85,120296,85,120348,85,120400,85,120452,85,1357,85,4608,85,66766,85,5196,85,42228,85,94018,85,71864,85,8744,118,8897,118,65366,118,8564,118,119855,118,119907,118,119959,118,120011,118,120063,118,120115,118,120167,118,120219,118,120271,118,120323,118,120375,118,120427,118,120479,118,7456,118,957,118,120526,118,120584,118,120642,118,120700,118,120758,118,1141,118,1496,118,71430,118,43945,118,71872,118,119309,86,1639,86,1783,86,8548,86,117995,86,119829,86,119881,86,119933,86,119985,86,120037,86,120089,86,120141,86,120193,86,120245,86,120297,86,120349,86,120401,86,120453,86,1140,86,11576,86,5081,86,5167,86,42719,86,42214,86,93960,86,71840,86,66845,86,623,119,119856,119,119908,119,119960,119,120012,119,120064,119,120116,119,120168,119,120220,119,120272,119,120324,119,120376,119,120428,119,120480,119,7457,119,1121,119,1309,119,1377,119,71434,119,71438,119,71439,119,43907,119,71910,87,71919,87,117996,87,119830,87,119882,87,119934,87,119986,87,120038,87,120090,87,120142,87,120194,87,120246,87,120298,87,120350,87,120402,87,120454,87,1308,87,5043,87,5076,87,42218,87,5742,120,10539,120,10540,120,10799,120,65368,120,8569,120,119857,120,119909,120,119961,120,120013,120,120065,120,120117,120,120169,120,120221,120,120273,120,120325,120,120377,120,120429,120,120481,120,5441,120,5501,120,5741,88,9587,88,66338,88,71916,88,65336,88,8553,88,117997,88,119831,88,119883,88,119935,88,119987,88,120039,88,120091,88,120143,88,120195,88,120247,88,120299,88,120351,88,120403,88,120455,88,42931,88,935,88,120510,88,120568,88,120626,88,120684,88,120742,88,11436,88,11613,88,5815,88,42219,88,66192,88,66228,88,66327,88,66855,88,611,121,7564,121,65369,121,119858,121,119910,121,119962,121,120014,121,120066,121,120118,121,120170,121,120222,121,120274,121,120326,121,120378,121,120430,121,120482,121,655,121,7935,121,43866,121,947,121,8509,121,120516,121,120574,121,120632,121,120690,121,120748,121,1199,121,4327,121,71900,121,65337,89,117998,89,119832,89,119884,89,119936,89,119988,89,120040,89,120092,89,120144,89,120196,89,120248,89,120300,89,120352,89,120404,89,120456,89,933,89,978,89,120508,89,120566,89,120624,89,120682,89,120740,89,11432,89,1198,89,5033,89,5053,89,42220,89,94019,89,71844,89,66226,89,119859,122,119911,122,119963,122,120015,122,120067,122,120119,122,120171,122,120223,122,120275,122,120327,122,120379,122,120431,122,120483,122,7458,122,43923,122,71876,122,71909,90,66293,90,65338,90,8484,90,8488,90,117999,90,119833,90,119885,90,119937,90,119989,90,120041,90,120197,90,120249,90,120301,90,120353,90,120405,90,120457,90,918,90,120493,90,120551,90,120609,90,120667,90,120725,90,5059,90,42204,90,71849,90,65282,34,65283,35,65284,36,65285,37,65286,38,65290,42,65291,43,65294,46,65295,47,65296,48,65298,50,65299,51,65300,52,65301,53,65302,54,65303,55,65304,56,65305,57,65308,60,65309,61,65310,62,65312,64,65316,68,65318,70,65319,71,65324,76,65329,81,65330,82,65333,85,65334,86,65335,87,65343,95,65346,98,65348,100,65350,102,65355,107,65357,109,65358,110,65361,113,65362,114,65364,116,65365,117,65367,119,65370,122,65371,123,65373,125,119846,109],"_default":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"cs":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"de":[65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"es":[8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"fr":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"it":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"ja":[8211,45,8218,44,65281,33,8216,96,8245,96,180,96,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65292,44,65297,49,65307,59],"ko":[8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"pl":[65374,126,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"pt-BR":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"qps-ploc":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"ru":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,305,105,921,73,1009,112,215,120,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"tr":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"zh-hans":[160,32,65374,126,8218,44,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65297,49],"zh-hant":[8211,45,65374,126,8218,44,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89]}'))}static{this.d=new Wr({getCacheKey:JSON.stringify},t=>{function r(u){const f=new Map;for(let h=0;h<u.length;h+=2)f.set(u[h],u[h+1]);return f}function n(u,f){const h=new Map(u);for(const[m,v]of f)h.set(m,v);return h}function i(u,f){if(!u)return f;const h=new Map;for(const[m,v]of u)f.has(m)&&h.set(m,v);return h}const s=this.c.value;let l=t.filter(u=>!u.startsWith("_")&&u in s);l.length===0&&(l=["_default"]);let o;for(const u of l){const f=r(s[u]);o=i(o,f)}const c=r(s._common),a=n(c,o);return new E1(a)})}static getInstance(t){return E1.d.get(Array.from(t))}static{this.e=new de(()=>Object.keys(E1.c.value).filter(t=>!t.startsWith("_")))}static getLocales(){return E1.e.value}constructor(t){this.f=t}isAmbiguous(t){return this.f.has(t)}containsAmbiguousCharacter(t){for(let r=0;r<t.length;r++){const n=t.codePointAt(r);if(typeof n=="number"&&this.isAmbiguous(n))return!0}return!1}getPrimaryConfusable(t){return this.f.get(t)}getConfusableCodePoints(){return new Set(this.f.keys())}},M2=class x1{static c(){return JSON.parse('{"_common":[11,12,13,127,847,1564,4447,4448,6068,6069,6155,6156,6157,6158,7355,7356,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8204,8205,8206,8207,8234,8235,8236,8237,8238,8239,8287,8288,8289,8290,8291,8292,8293,8294,8295,8296,8297,8298,8299,8300,8301,8302,8303,10240,12644,65024,65025,65026,65027,65028,65029,65030,65031,65032,65033,65034,65035,65036,65037,65038,65039,65279,65440,65520,65521,65522,65523,65524,65525,65526,65527,65528,65532,78844,119155,119156,119157,119158,119159,119160,119161,119162,917504,917505,917506,917507,917508,917509,917510,917511,917512,917513,917514,917515,917516,917517,917518,917519,917520,917521,917522,917523,917524,917525,917526,917527,917528,917529,917530,917531,917532,917533,917534,917535,917536,917537,917538,917539,917540,917541,917542,917543,917544,917545,917546,917547,917548,917549,917550,917551,917552,917553,917554,917555,917556,917557,917558,917559,917560,917561,917562,917563,917564,917565,917566,917567,917568,917569,917570,917571,917572,917573,917574,917575,917576,917577,917578,917579,917580,917581,917582,917583,917584,917585,917586,917587,917588,917589,917590,917591,917592,917593,917594,917595,917596,917597,917598,917599,917600,917601,917602,917603,917604,917605,917606,917607,917608,917609,917610,917611,917612,917613,917614,917615,917616,917617,917618,917619,917620,917621,917622,917623,917624,917625,917626,917627,917628,917629,917630,917631,917760,917761,917762,917763,917764,917765,917766,917767,917768,917769,917770,917771,917772,917773,917774,917775,917776,917777,917778,917779,917780,917781,917782,917783,917784,917785,917786,917787,917788,917789,917790,917791,917792,917793,917794,917795,917796,917797,917798,917799,917800,917801,917802,917803,917804,917805,917806,917807,917808,917809,917810,917811,917812,917813,917814,917815,917816,917817,917818,917819,917820,917821,917822,917823,917824,917825,917826,917827,917828,917829,917830,917831,917832,917833,917834,917835,917836,917837,917838,917839,917840,917841,917842,917843,917844,917845,917846,917847,917848,917849,917850,917851,917852,917853,917854,917855,917856,917857,917858,917859,917860,917861,917862,917863,917864,917865,917866,917867,917868,917869,917870,917871,917872,917873,917874,917875,917876,917877,917878,917879,917880,917881,917882,917883,917884,917885,917886,917887,917888,917889,917890,917891,917892,917893,917894,917895,917896,917897,917898,917899,917900,917901,917902,917903,917904,917905,917906,917907,917908,917909,917910,917911,917912,917913,917914,917915,917916,917917,917918,917919,917920,917921,917922,917923,917924,917925,917926,917927,917928,917929,917930,917931,917932,917933,917934,917935,917936,917937,917938,917939,917940,917941,917942,917943,917944,917945,917946,917947,917948,917949,917950,917951,917952,917953,917954,917955,917956,917957,917958,917959,917960,917961,917962,917963,917964,917965,917966,917967,917968,917969,917970,917971,917972,917973,917974,917975,917976,917977,917978,917979,917980,917981,917982,917983,917984,917985,917986,917987,917988,917989,917990,917991,917992,917993,917994,917995,917996,917997,917998,917999],"cs":[173,8203,12288],"de":[173,8203,12288],"es":[8203,12288],"fr":[173,8203,12288],"it":[160,173,12288],"ja":[173],"ko":[173,12288],"pl":[173,8203,12288],"pt-BR":[173,8203,12288],"qps-ploc":[160,173,8203,12288],"ru":[173,12288],"tr":[160,173,8203,12288],"zh-hans":[160,173,8203,12288],"zh-hant":[173,12288]}')}static{this.d=void 0}static e(){return this.d||(this.d=new Set([...Object.values(x1.c())].flat())),this.d}static isInvisibleCharacter(t){return x1.e().has(t)}static containsInvisibleCharacter(t){for(let r=0;r<t.length;r++){const n=t.codePointAt(r);if(typeof n=="number"&&(x1.isInvisibleCharacter(n)||n===32))return!0}return!1}static get codePoints(){return x1.e()}},ve="default",Xr="$initialize",it;(function(e){e[e.Request=0]="Request",e[e.Reply=1]="Reply",e[e.SubscribeEvent=2]="SubscribeEvent",e[e.Event=3]="Event",e[e.UnsubscribeEvent=4]="UnsubscribeEvent"})(it||(it={}));var en=class{constructor(e,t,r,n,i){this.vsWorker=e,this.req=t,this.channel=r,this.method=n,this.args=i,this.type=0}},st=class{constructor(e,t,r,n){this.vsWorker=e,this.seq=t,this.res=r,this.err=n,this.type=1}},tn=class{constructor(e,t,r,n,i){this.vsWorker=e,this.req=t,this.channel=r,this.eventName=n,this.arg=i,this.type=2}},rn=class{constructor(e,t,r){this.vsWorker=e,this.req=t,this.event=r,this.type=3}},nn=class{constructor(e,t){this.vsWorker=e,this.req=t,this.type=4}},sn=class{constructor(e){this.a=-1,this.g=e,this.b=0,this.c=Object.create(null),this.d=new Map,this.f=new Map}setWorkerId(e){this.a=e}sendMessage(e,t,r){const n=String(++this.b);return new Promise((i,s)=>{this.c[n]={resolve:i,reject:s},this.o(new en(this.a,n,e,t,r))})}listen(e,t,r){let n=null;const i=new Y({onWillAddFirstListener:()=>{n=String(++this.b),this.d.set(n,i),this.o(new tn(this.a,n,e,t,r))},onDidRemoveLastListener:()=>{this.d.delete(n),this.o(new nn(this.a,n)),n=null}});return i.event}handleMessage(e){!e||!e.vsWorker||this.a!==-1&&e.vsWorker!==this.a||this.h(e)}createProxyToRemoteChannel(e,t){const r={get:(n,i)=>(typeof i=="string"&&!n[i]&&(lt(i)?n[i]=s=>this.listen(e,i,s):ot(i)?n[i]=this.listen(e,i,void 0):i.charCodeAt(0)===36&&(n[i]=async(...s)=>(await t?.(),this.sendMessage(e,i,s)))),n[i])};return new Proxy(Object.create(null),r)}h(e){switch(e.type){case 1:return this.j(e);case 0:return this.k(e);case 2:return this.l(e);case 3:return this.m(e);case 4:return this.n(e)}}j(e){if(!this.c[e.seq]){console.warn("Got reply to unknown seq");return}const t=this.c[e.seq];if(delete this.c[e.seq],e.err){let r=e.err;e.err.$isError&&(r=new Error,r.name=e.err.name,r.message=e.err.message,r.stack=e.err.stack),t.reject(r);return}t.resolve(e.res)}k(e){const t=e.req;this.g.handleMessage(e.channel,e.method,e.args).then(n=>{this.o(new st(this.a,t,n,void 0))},n=>{n.detail instanceof Error&&(n.detail=te(n.detail)),this.o(new st(this.a,t,void 0,te(n)))})}l(e){const t=e.req,r=this.g.handleEvent(e.channel,e.eventName,e.arg)(n=>{this.o(new rn(this.a,t,n))});this.f.set(t,r)}m(e){if(!this.d.has(e.req)){console.warn("Got event for unknown req");return}this.d.get(e.req).fire(e.event)}n(e){if(!this.f.has(e.req)){console.warn("Got unsubscribe for unknown req");return}this.f.get(e.req).dispose(),this.f.delete(e.req)}o(e){const t=[];if(e.type===0)for(let r=0;r<e.args.length;r++)e.args[r]instanceof ArrayBuffer&&t.push(e.args[r]);else e.type===1&&e.res instanceof ArrayBuffer&&t.push(e.res);this.g.sendMessage(e,t)}};function ot(e){return e[0]==="o"&&e[1]==="n"&&tt(e.charCodeAt(2))}function lt(e){return/^onDynamic/.test(e)&&tt(e.charCodeAt(9))}var on=class{constructor(e,t){this.b=new Map,this.c=new Map,this.a=new sn({sendMessage:(r,n)=>{e(r,n)},handleMessage:(r,n,i)=>this.d(r,n,i),handleEvent:(r,n,i)=>this.f(r,n,i)}),this.requestHandler=t(this)}onmessage(e){this.a.handleMessage(e)}d(e,t,r){if(e===ve&&t===Xr)return this.g(r[0]);const n=e===ve?this.requestHandler:this.b.get(e);if(!n)return Promise.reject(new Error(`Missing channel ${e} on worker thread`));if(typeof n[t]!="function")return Promise.reject(new Error(`Missing method ${t} on worker thread channel ${e}`));try{return Promise.resolve(n[t].apply(n,r))}catch(i){return Promise.reject(i)}}f(e,t,r){const n=e===ve?this.requestHandler:this.b.get(e);if(!n)throw new Error(`Missing channel ${e} on worker thread`);if(lt(t)){const i=n[t].call(n,r);if(typeof i!="function")throw new Error(`Missing dynamic event ${t} on request handler.`);return i}if(ot(t)){const i=n[t];if(typeof i!="function")throw new Error(`Missing event ${t} on request handler.`);return i}throw new Error(`Malformed event name ${t}`)}setChannel(e,t){this.b.set(e,t)}getChannel(e){if(!this.c.has(e)){const t=this.a.createProxyToRemoteChannel(e);this.c.set(e,t)}return this.c.get(e)}async g(e){this.a.setWorkerId(e)}},ge=!1;function ln(e){if(ge)throw new Error("WebWorker already initialized!");ge=!0;const t=new on(r=>globalThis.postMessage(r),r=>e(r));return globalThis.onmessage=r=>{t.onmessage(r.data)},t}function an(e){globalThis.onmessage=t=>{ge||ln(e)}}var u1,pe=globalThis.vscode;if(typeof pe<"u"&&typeof pe.process<"u"){const e=pe.process;u1={get platform(){return e.platform},get arch(){return e.arch},get env(){return e.env},cwd(){return e.cwd()}}}else typeof process<"u"&&typeof process?.versions?.node=="string"?u1={get platform(){return process.platform},get arch(){return process.arch},get env(){return process.env},cwd(){return process.env.VSCODE_CWD||process.cwd()}}:u1={get platform(){return g1?"win32":Or?"darwin":"linux"},get arch(){},get env(){return{}},cwd(){return"/"}};var U1=u1.cwd,cn=u1.env,un=u1.platform,j2=u1.arch,fn=65,hn=97,dn=90,mn=122,f1=46,q=47,B=92,n1=58,vn=63,at=class extends Error{constructor(e,t,r){let n;typeof t=="string"&&t.indexOf("not ")===0?(n="must not be",t=t.replace(/^not /,"")):n="must be";const i=e.indexOf(".")!==-1?"property":"argument";let s=`The "${e}" ${i} ${n} of type ${t}`;s+=`. Received type ${typeof r}`,super(s),this.code="ERR_INVALID_ARG_TYPE"}};function gn(e,t){if(e===null||typeof e!="object")throw new at(t,"Object",e)}function I(e,t){if(typeof e!="string")throw new at(t,"string",e)}var H=un==="win32";function E(e){return e===q||e===B}function we(e){return e===q}function i1(e){return e>=fn&&e<=dn||e>=hn&&e<=mn}function q1(e,t,r,n){let i="",s=0,l=-1,o=0,c=0;for(let a=0;a<=e.length;++a){if(a<e.length)c=e.charCodeAt(a);else{if(n(c))break;c=q}if(n(c)){if(!(l===a-1||o===1))if(o===2){if(i.length<2||s!==2||i.charCodeAt(i.length-1)!==f1||i.charCodeAt(i.length-2)!==f1){if(i.length>2){const u=i.lastIndexOf(r);u===-1?(i="",s=0):(i=i.slice(0,u),s=i.length-1-i.lastIndexOf(r)),l=a,o=0;continue}else if(i.length!==0){i="",s=0,l=a,o=0;continue}}t&&(i+=i.length>0?`${r}..`:"..",s=2)}else i.length>0?i+=`${r}${e.slice(l+1,a)}`:i=e.slice(l+1,a),s=a-l-1;l=a,o=0}else c===f1&&o!==-1?++o:o=-1}return i}function pn(e){return e?`${e[0]==="."?"":"."}${e}`:""}function ct(e,t){gn(t,"pathObject");const r=t.dir||t.root,n=t.base||`${t.name||""}${pn(t.ext)}`;return r?r===t.root?`${r}${n}`:`${r}${e}${n}`:n}var D={resolve(...e){let t="",r="",n=!1;for(let i=e.length-1;i>=-1;i--){let s;if(i>=0){if(s=e[i],I(s,`paths[${i}]`),s.length===0)continue}else t.length===0?s=U1():(s=cn[`=${t}`]||U1(),(s===void 0||s.slice(0,2).toLowerCase()!==t.toLowerCase()&&s.charCodeAt(2)===B)&&(s=`${t}\\`));const l=s.length;let o=0,c="",a=!1;const u=s.charCodeAt(0);if(l===1)E(u)&&(o=1,a=!0);else if(E(u))if(a=!0,E(s.charCodeAt(1))){let f=2,h=f;for(;f<l&&!E(s.charCodeAt(f));)f++;if(f<l&&f!==h){const m=s.slice(h,f);for(h=f;f<l&&E(s.charCodeAt(f));)f++;if(f<l&&f!==h){for(h=f;f<l&&!E(s.charCodeAt(f));)f++;(f===l||f!==h)&&(c=`\\\\${m}\\${s.slice(h,f)}`,o=f)}}}else o=1;else i1(u)&&s.charCodeAt(1)===n1&&(c=s.slice(0,2),o=2,l>2&&E(s.charCodeAt(2))&&(a=!0,o=3));if(c.length>0)if(t.length>0){if(c.toLowerCase()!==t.toLowerCase())continue}else t=c;if(n){if(t.length>0)break}else if(r=`${s.slice(o)}\\${r}`,n=a,a&&t.length>0)break}return r=q1(r,!n,"\\",E),n?`${t}\\${r}`:`${t}${r}`||"."},normalize(e){I(e,"path");const t=e.length;if(t===0)return".";let r=0,n,i=!1;const s=e.charCodeAt(0);if(t===1)return we(s)?"\\":e;if(E(s))if(i=!0,E(e.charCodeAt(1))){let o=2,c=o;for(;o<t&&!E(e.charCodeAt(o));)o++;if(o<t&&o!==c){const a=e.slice(c,o);for(c=o;o<t&&E(e.charCodeAt(o));)o++;if(o<t&&o!==c){for(c=o;o<t&&!E(e.charCodeAt(o));)o++;if(o===t)return`\\\\${a}\\${e.slice(c)}\\`;o!==c&&(n=`\\\\${a}\\${e.slice(c,o)}`,r=o)}}}else r=1;else i1(s)&&e.charCodeAt(1)===n1&&(n=e.slice(0,2),r=2,t>2&&E(e.charCodeAt(2))&&(i=!0,r=3));let l=r<t?q1(e.slice(r),!i,"\\",E):"";if(l.length===0&&!i&&(l="."),l.length>0&&E(e.charCodeAt(t-1))&&(l+="\\"),!i&&n===void 0&&e.includes(":")){if(l.length>=2&&i1(l.charCodeAt(0))&&l.charCodeAt(1)===n1)return`.\\${l}`;let o=e.indexOf(":");do if(o===t-1||E(e.charCodeAt(o+1)))return`.\\${l}`;while((o=e.indexOf(":",o+1))!==-1)}return n===void 0?i?`\\${l}`:l:i?`${n}\\${l}`:`${n}${l}`},isAbsolute(e){I(e,"path");const t=e.length;if(t===0)return!1;const r=e.charCodeAt(0);return E(r)||t>2&&i1(r)&&e.charCodeAt(1)===n1&&E(e.charCodeAt(2))},join(...e){if(e.length===0)return".";let t,r;for(let s=0;s<e.length;++s){const l=e[s];I(l,"path"),l.length>0&&(t===void 0?t=r=l:t+=`\\${l}`)}if(t===void 0)return".";let n=!0,i=0;if(typeof r=="string"&&E(r.charCodeAt(0))){++i;const s=r.length;s>1&&E(r.charCodeAt(1))&&(++i,s>2&&(E(r.charCodeAt(2))?++i:n=!1))}if(n){for(;i<t.length&&E(t.charCodeAt(i));)i++;i>=2&&(t=`\\${t.slice(i)}`)}return D.normalize(t)},relative(e,t){if(I(e,"from"),I(t,"to"),e===t)return"";const r=D.resolve(e),n=D.resolve(t);if(r===n||(e=r.toLowerCase(),t=n.toLowerCase(),e===t))return"";if(r.length!==e.length||n.length!==t.length){const v=r.split("\\"),y=n.split("\\");v[v.length-1]===""&&v.pop(),y[y.length-1]===""&&y.pop();const g=v.length,N=y.length,A=g<N?g:N;let _;for(_=0;_<A&&v[_].toLowerCase()===y[_].toLowerCase();_++);return _===0?n:_===A?N>A?y.slice(_).join("\\"):g>A?"..\\".repeat(g-1-_)+"..":"":"..\\".repeat(g-_)+y.slice(_).join("\\")}let i=0;for(;i<e.length&&e.charCodeAt(i)===B;)i++;let s=e.length;for(;s-1>i&&e.charCodeAt(s-1)===B;)s--;const l=s-i;let o=0;for(;o<t.length&&t.charCodeAt(o)===B;)o++;let c=t.length;for(;c-1>o&&t.charCodeAt(c-1)===B;)c--;const a=c-o,u=l<a?l:a;let f=-1,h=0;for(;h<u;h++){const v=e.charCodeAt(i+h);if(v!==t.charCodeAt(o+h))break;v===B&&(f=h)}if(h!==u){if(f===-1)return n}else{if(a>u){if(t.charCodeAt(o+h)===B)return n.slice(o+h+1);if(h===2)return n.slice(o+h)}l>u&&(e.charCodeAt(i+h)===B?f=h:h===2&&(f=3)),f===-1&&(f=0)}let m="";for(h=i+f+1;h<=s;++h)(h===s||e.charCodeAt(h)===B)&&(m+=m.length===0?"..":"\\..");return o+=f,m.length>0?`${m}${n.slice(o,c)}`:(n.charCodeAt(o)===B&&++o,n.slice(o,c))},toNamespacedPath(e){if(typeof e!="string"||e.length===0)return e;const t=D.resolve(e);if(t.length<=2)return e;if(t.charCodeAt(0)===B){if(t.charCodeAt(1)===B){const r=t.charCodeAt(2);if(r!==vn&&r!==f1)return`\\\\?\\UNC\\${t.slice(2)}`}}else if(i1(t.charCodeAt(0))&&t.charCodeAt(1)===n1&&t.charCodeAt(2)===B)return`\\\\?\\${t}`;return t},dirname(e){I(e,"path");const t=e.length;if(t===0)return".";let r=-1,n=0;const i=e.charCodeAt(0);if(t===1)return E(i)?e:".";if(E(i)){if(r=n=1,E(e.charCodeAt(1))){let o=2,c=o;for(;o<t&&!E(e.charCodeAt(o));)o++;if(o<t&&o!==c){for(c=o;o<t&&E(e.charCodeAt(o));)o++;if(o<t&&o!==c){for(c=o;o<t&&!E(e.charCodeAt(o));)o++;if(o===t)return e;o!==c&&(r=n=o+1)}}}}else i1(i)&&e.charCodeAt(1)===n1&&(r=t>2&&E(e.charCodeAt(2))?3:2,n=r);let s=-1,l=!0;for(let o=t-1;o>=n;--o)if(E(e.charCodeAt(o))){if(!l){s=o;break}}else l=!1;if(s===-1){if(r===-1)return".";s=r}return e.slice(0,s)},basename(e,t){t!==void 0&&I(t,"suffix"),I(e,"path");let r=0,n=-1,i=!0,s;if(e.length>=2&&i1(e.charCodeAt(0))&&e.charCodeAt(1)===n1&&(r=2),t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let l=t.length-1,o=-1;for(s=e.length-1;s>=r;--s){const c=e.charCodeAt(s);if(E(c)){if(!i){r=s+1;break}}else o===-1&&(i=!1,o=s+1),l>=0&&(c===t.charCodeAt(l)?--l===-1&&(n=s):(l=-1,n=o))}return r===n?n=o:n===-1&&(n=e.length),e.slice(r,n)}for(s=e.length-1;s>=r;--s)if(E(e.charCodeAt(s))){if(!i){r=s+1;break}}else n===-1&&(i=!1,n=s+1);return n===-1?"":e.slice(r,n)},extname(e){I(e,"path");let t=0,r=-1,n=0,i=-1,s=!0,l=0;e.length>=2&&e.charCodeAt(1)===n1&&i1(e.charCodeAt(0))&&(t=n=2);for(let o=e.length-1;o>=t;--o){const c=e.charCodeAt(o);if(E(c)){if(!s){n=o+1;break}continue}i===-1&&(s=!1,i=o+1),c===f1?r===-1?r=o:l!==1&&(l=1):r!==-1&&(l=-1)}return r===-1||i===-1||l===0||l===1&&r===i-1&&r===n+1?"":e.slice(r,i)},format:ct.bind(null,"\\"),parse(e){I(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const r=e.length;let n=0,i=e.charCodeAt(0);if(r===1)return E(i)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(E(i)){if(n=1,E(e.charCodeAt(1))){let f=2,h=f;for(;f<r&&!E(e.charCodeAt(f));)f++;if(f<r&&f!==h){for(h=f;f<r&&E(e.charCodeAt(f));)f++;if(f<r&&f!==h){for(h=f;f<r&&!E(e.charCodeAt(f));)f++;f===r?n=f:f!==h&&(n=f+1)}}}}else if(i1(i)&&e.charCodeAt(1)===n1){if(r<=2)return t.root=t.dir=e,t;if(n=2,E(e.charCodeAt(2))){if(r===3)return t.root=t.dir=e,t;n=3}}n>0&&(t.root=e.slice(0,n));let s=-1,l=n,o=-1,c=!0,a=e.length-1,u=0;for(;a>=n;--a){if(i=e.charCodeAt(a),E(i)){if(!c){l=a+1;break}continue}o===-1&&(c=!1,o=a+1),i===f1?s===-1?s=a:u!==1&&(u=1):s!==-1&&(u=-1)}return o!==-1&&(s===-1||u===0||u===1&&s===o-1&&s===l+1?t.base=t.name=e.slice(l,o):(t.name=e.slice(l,s),t.base=e.slice(l,o),t.ext=e.slice(s,o))),l>0&&l!==n?t.dir=e.slice(0,l-1):t.dir=t.root,t},sep:"\\",delimiter:";",win32:null,posix:null},wn=(()=>{if(H){const e=/\\/g;return()=>{const t=U1().replace(e,"/");return t.slice(t.indexOf("/"))}}return()=>U1()})(),S={resolve(...e){let t="",r=!1;for(let n=e.length-1;n>=0&&!r;n--){const i=e[n];I(i,`paths[${n}]`),i.length!==0&&(t=`${i}/${t}`,r=i.charCodeAt(0)===q)}if(!r){const n=wn();t=`${n}/${t}`,r=n.charCodeAt(0)===q}return t=q1(t,!r,"/",we),r?`/${t}`:t.length>0?t:"."},normalize(e){if(I(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===q,r=e.charCodeAt(e.length-1)===q;return e=q1(e,!t,"/",we),e.length===0?t?"/":r?"./":".":(r&&(e+="/"),t?`/${e}`:e)},isAbsolute(e){return I(e,"path"),e.length>0&&e.charCodeAt(0)===q},join(...e){if(e.length===0)return".";const t=[];for(let r=0;r<e.length;++r){const n=e[r];I(n,"path"),n.length>0&&t.push(n)}return t.length===0?".":S.normalize(t.join("/"))},relative(e,t){if(I(e,"from"),I(t,"to"),e===t||(e=S.resolve(e),t=S.resolve(t),e===t))return"";const r=1,n=e.length,i=n-r,s=1,l=t.length-s,o=i<l?i:l;let c=-1,a=0;for(;a<o;a++){const f=e.charCodeAt(r+a);if(f!==t.charCodeAt(s+a))break;f===q&&(c=a)}if(a===o)if(l>o){if(t.charCodeAt(s+a)===q)return t.slice(s+a+1);if(a===0)return t.slice(s+a)}else i>o&&(e.charCodeAt(r+a)===q?c=a:a===0&&(c=0));let u="";for(a=r+c+1;a<=n;++a)(a===n||e.charCodeAt(a)===q)&&(u+=u.length===0?"..":"/..");return`${u}${t.slice(s+c)}`},toNamespacedPath(e){return e},dirname(e){if(I(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===q;let r=-1,n=!0;for(let i=e.length-1;i>=1;--i)if(e.charCodeAt(i)===q){if(!n){r=i;break}}else n=!1;return r===-1?t?"/":".":t&&r===1?"//":e.slice(0,r)},basename(e,t){t!==void 0&&I(t,"suffix"),I(e,"path");let r=0,n=-1,i=!0,s;if(t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let l=t.length-1,o=-1;for(s=e.length-1;s>=0;--s){const c=e.charCodeAt(s);if(c===q){if(!i){r=s+1;break}}else o===-1&&(i=!1,o=s+1),l>=0&&(c===t.charCodeAt(l)?--l===-1&&(n=s):(l=-1,n=o))}return r===n?n=o:n===-1&&(n=e.length),e.slice(r,n)}for(s=e.length-1;s>=0;--s)if(e.charCodeAt(s)===q){if(!i){r=s+1;break}}else n===-1&&(i=!1,n=s+1);return n===-1?"":e.slice(r,n)},extname(e){I(e,"path");let t=-1,r=0,n=-1,i=!0,s=0;for(let l=e.length-1;l>=0;--l){const o=e[l];if(o==="/"){if(!i){r=l+1;break}continue}n===-1&&(i=!1,n=l+1),o==="."?t===-1?t=l:s!==1&&(s=1):t!==-1&&(s=-1)}return t===-1||n===-1||s===0||s===1&&t===n-1&&t===r+1?"":e.slice(t,n)},format:ct.bind(null,"/"),parse(e){I(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const r=e.charCodeAt(0)===q;let n;r?(t.root="/",n=1):n=0;let i=-1,s=0,l=-1,o=!0,c=e.length-1,a=0;for(;c>=n;--c){const u=e.charCodeAt(c);if(u===q){if(!o){s=c+1;break}continue}l===-1&&(o=!1,l=c+1),u===f1?i===-1?i=c:a!==1&&(a=1):i!==-1&&(a=-1)}if(l!==-1){const u=s===0&&r?1:s;i===-1||a===0||a===1&&i===l-1&&i===s+1?t.base=t.name=e.slice(u,l):(t.name=e.slice(u,i),t.base=e.slice(u,l),t.ext=e.slice(i,l))}return s>0?t.dir=e.slice(0,s-1):r&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};S.win32=D.win32=D,S.posix=D.posix=S;var bn=H?D.normalize:S.normalize,U2=H?D.isAbsolute:S.isAbsolute,yn=H?D.join:S.join,Cn=H?D.resolve:S.resolve,ut=H?D.relative:S.relative,Ln=H?D.dirname:S.dirname,$n=H?D.basename:S.basename,An=H?D.extname:S.extname,q2=H?D.format:S.format,F2=H?D.parse:S.parse,W2=H?D.toNamespacedPath:S.toNamespacedPath,l1=H?D.sep:S.sep,z2=H?D.delimiter:S.delimiter;function a1(e){return e===47||e===92}function ft(e){return e.replace(/[\\/]/g,S.sep)}function En(e){return e.indexOf("/")===-1&&(e=ft(e)),/^[a-zA-Z]:(\/|$)/.test(e)&&(e="/"+e),e}function ht(e,t=S.sep){if(!e)return"";const r=e.length,n=e.charCodeAt(0);if(a1(n)){if(a1(e.charCodeAt(1))&&!a1(e.charCodeAt(2))){let s=3;const l=s;for(;s<r&&!a1(e.charCodeAt(s));s++);if(l!==s&&!a1(e.charCodeAt(s+1))){for(s+=1;s<r;s++)if(a1(e.charCodeAt(s)))return e.slice(0,s+1).replace(/[\\/]/g,t)}}return t}else if(xn(n)&&e.charCodeAt(1)===58)return a1(e.charCodeAt(2))?e.slice(0,2)+t:e.slice(0,2);let i=e.indexOf("://");if(i!==-1){for(i+=3;i<r;i++)if(a1(e.charCodeAt(i)))return e.slice(0,i+1)}return""}function be(e,t,r,n=l1){if(e===t)return!0;if(!e||!t||t.length>e.length)return!1;if(r){if(!Kr(e,t))return!1;if(t.length===e.length)return!0;let s=t.length;return t.charAt(t.length-1)===n&&s--,e.charAt(s)===n}return t.charAt(t.length-1)!==n&&(t+=n),e.indexOf(t)===0}function xn(e){return e>=65&&e<=90||e>=97&&e<=122}var Nn=/^\w[\w\d+.-]*$/,Pn=/^\//,_n=/^\/\//;function Sn(e,t){if(!e.scheme&&t)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!Nn.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path){if(e.authority){if(!Pn.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(_n.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}function kn(e,t){return!e&&!t?"file":e}function On(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==Z&&(t=Z+t):t=Z;break}return t}var T="",Z="/",Rn=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,W=class G1{static isUri(t){return t instanceof G1?!0:t?typeof t.authority=="string"&&typeof t.fragment=="string"&&typeof t.path=="string"&&typeof t.query=="string"&&typeof t.scheme=="string"&&typeof t.fsPath=="string"&&typeof t.with=="function"&&typeof t.toString=="function":!1}constructor(t,r,n,i,s,l=!1){typeof t=="object"?(this.scheme=t.scheme||T,this.authority=t.authority||T,this.path=t.path||T,this.query=t.query||T,this.fragment=t.fragment||T):(this.scheme=kn(t,l),this.authority=r||T,this.path=On(this.scheme,n||T),this.query=i||T,this.fragment=s||T,Sn(this,l))}get fsPath(){return F1(this,!1)}with(t){if(!t)return this;let{scheme:r,authority:n,path:i,query:s,fragment:l}=t;return r===void 0?r=this.scheme:r===null&&(r=T),n===void 0?n=this.authority:n===null&&(n=T),i===void 0?i=this.path:i===null&&(i=T),s===void 0?s=this.query:s===null&&(s=T),l===void 0?l=this.fragment:l===null&&(l=T),r===this.scheme&&n===this.authority&&i===this.path&&s===this.query&&l===this.fragment?this:new p1(r,n,i,s,l)}static parse(t,r=!1){const n=Rn.exec(t);return n?new p1(n[2]||T,W1(n[4]||T),W1(n[5]||T),W1(n[7]||T),W1(n[9]||T),r):new p1(T,T,T,T,T)}static file(t){let r=T;if(g1&&(t=t.replace(/\\/g,Z)),t[0]===Z&&t[1]===Z){const n=t.indexOf(Z,2);n===-1?(r=t.substring(2),t=Z):(r=t.substring(2,n),t=t.substring(n)||Z)}return new p1("file",r,t,T,T)}static from(t,r){return new p1(t.scheme,t.authority,t.path,t.query,t.fragment,r)}static joinPath(t,...r){if(!t.path)throw new Error("[UriError]: cannot call joinPath on URI without path");let n;return g1&&t.scheme==="file"?n=G1.file(D.join(F1(t,!0),...r)).path:n=S.join(t.path,...r),t.with({path:n})}toString(t=!1){return ye(this,t)}toJSON(){return this}static revive(t){if(t){if(t instanceof G1)return t;{const r=new p1(t);return r._formatted=t.external??null,r._fsPath=t._sep===dt?t.fsPath??null:null,r}}else return t}[Symbol.for("debug.description")](){return`URI(${this.toString()})`}},dt=g1?1:void 0,p1=class extends W{constructor(){super(...arguments),this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||(this._fsPath=F1(this,!1)),this._fsPath}toString(e=!1){return e?ye(this,!0):(this._formatted||(this._formatted=ye(this,!1)),this._formatted)}toJSON(){const e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=dt),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}},mt={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function vt(e,t,r){let n,i=-1;for(let s=0;s<e.length;s++){const l=e.charCodeAt(s);if(l>=97&&l<=122||l>=65&&l<=90||l>=48&&l<=57||l===45||l===46||l===95||l===126||t&&l===47||r&&l===91||r&&l===93||r&&l===58)i!==-1&&(n+=encodeURIComponent(e.substring(i,s)),i=-1),n!==void 0&&(n+=e.charAt(s));else{n===void 0&&(n=e.substr(0,s));const o=mt[l];o!==void 0?(i!==-1&&(n+=encodeURIComponent(e.substring(i,s)),i=-1),n+=o):i===-1&&(i=s)}}return i!==-1&&(n+=encodeURIComponent(e.substring(i))),n!==void 0?n:e}function Tn(e){let t;for(let r=0;r<e.length;r++){const n=e.charCodeAt(r);n===35||n===63?(t===void 0&&(t=e.substr(0,r)),t+=mt[n]):t!==void 0&&(t+=e[r])}return t!==void 0?t:e}function F1(e,t){let r;return e.authority&&e.path.length>1&&e.scheme==="file"?r=`//${e.authority}${e.path}`:e.path.charCodeAt(0)===47&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&e.path.charCodeAt(2)===58?t?r=e.path.substr(1):r=e.path[1].toLowerCase()+e.path.substr(2):r=e.path,g1&&(r=r.replace(/\//g,"\\")),r}function ye(e,t){const r=t?Tn:vt;let n="",{scheme:i,authority:s,path:l,query:o,fragment:c}=e;if(i&&(n+=i,n+=":"),(s||i==="file")&&(n+=Z,n+=Z),s){let a=s.indexOf("@");if(a!==-1){const u=s.substr(0,a);s=s.substr(a+1),a=u.lastIndexOf(":"),a===-1?n+=r(u,!1,!1):(n+=r(u.substr(0,a),!1,!1),n+=":",n+=r(u.substr(a+1),!1,!0)),n+="@"}s=s.toLowerCase(),a=s.lastIndexOf(":"),a===-1?n+=r(s,!1,!0):(n+=r(s.substr(0,a),!1,!0),n+=s.substr(a))}if(l){if(l.length>=3&&l.charCodeAt(0)===47&&l.charCodeAt(2)===58){const a=l.charCodeAt(1);a>=65&&a<=90&&(l=`/${String.fromCharCode(a+32)}:${l.substr(3)}`)}else if(l.length>=2&&l.charCodeAt(1)===58){const a=l.charCodeAt(0);a>=65&&a<=90&&(l=`${String.fromCharCode(a+32)}:${l.substr(2)}`)}n+=r(l,!0,!1)}return o&&(n+="?",n+=r(o,!1,!1)),c&&(n+="#",n+=t?c:vt(c,!1,!1)),n}function gt(e){try{return decodeURIComponent(e)}catch{return e.length>3?e.substr(0,3)+gt(e.substr(3)):e}}var pt=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function W1(e){return e.match(pt)?e.replace(pt,t=>gt(t)):e}var F;(function(e){e.inMemory="inmemory",e.vscode="vscode",e.internal="private",e.walkThrough="walkThrough",e.walkThroughSnippet="walkThroughSnippet",e.http="http",e.https="https",e.file="file",e.mailto="mailto",e.untitled="untitled",e.data="data",e.command="command",e.vscodeRemote="vscode-remote",e.vscodeRemoteResource="vscode-remote-resource",e.vscodeManagedRemoteResource="vscode-managed-remote-resource",e.vscodeUserData="vscode-userdata",e.vscodeCustomEditor="vscode-custom-editor",e.vscodeNotebookCell="vscode-notebook-cell",e.vscodeNotebookCellMetadata="vscode-notebook-cell-metadata",e.vscodeNotebookCellMetadataDiff="vscode-notebook-cell-metadata-diff",e.vscodeNotebookCellOutput="vscode-notebook-cell-output",e.vscodeNotebookCellOutputDiff="vscode-notebook-cell-output-diff",e.vscodeNotebookMetadata="vscode-notebook-metadata",e.vscodeInteractiveInput="vscode-interactive-input",e.vscodeSettings="vscode-settings",e.vscodeWorkspaceTrust="vscode-workspace-trust",e.vscodeTerminal="vscode-terminal",e.vscodeChatCodeBlock="vscode-chat-code-block",e.vscodeChatCodeCompareBlock="vscode-chat-code-compare-block",e.vscodeChatSesssion="vscode-chat-editor",e.webviewPanel="webview-panel",e.vscodeWebview="vscode-webview",e.extension="extension",e.vscodeFileResource="vscode-file",e.tmp="tmp",e.vsls="vsls",e.vscodeSourceControl="vscode-scm",e.commentsInput="comment",e.codeSetting="code-setting",e.outputChannel="output",e.accessibleView="accessible-view"})(F||(F={}));var In="tkn",Dn=class{constructor(){this.a=Object.create(null),this.b=Object.create(null),this.c=Object.create(null),this.d="http",this.e=null,this.f="/"}setPreferredWebSchema(e){this.d=e}setDelegate(e){this.e=e}setServerRootPath(e,t){this.f=S.join(t??"/",jn(e))}getServerRootPath(){return this.f}get g(){return S.join(this.f,F.vscodeRemoteResource)}set(e,t,r){this.a[e]=t,this.b[e]=r}setConnectionToken(e,t){this.c[e]=t}getPreferredWebSchema(){return this.d}rewrite(e){if(this.e)try{return this.e(e)}catch(l){return S1(l),e}const t=e.authority;let r=this.a[t];r&&r.indexOf(":")!==-1&&r.indexOf("[")===-1&&(r=`[${r}]`);const n=this.b[t],i=this.c[t];let s=`path=${encodeURIComponent(e.path)}`;return typeof i=="string"&&(s+=`&${In}=${encodeURIComponent(i)}`),W.from({scheme:Tr?this.d:F.vscodeRemoteResource,authority:`${r}:${n}`,path:this.g,query:s})}},Mn=new Dn;function jn(e){return`${e.quality??"oss"}-${e.commit??"dev"}`}var Un="vscode-app",qn=class Y1{static{this.a=Un}asBrowserUri(t){const r=this.b(t);return this.uriToBrowserUri(r)}uriToBrowserUri(t){return t.scheme===F.vscodeRemote?Mn.rewrite(t):t.scheme===F.file&&(Rr||Dr===`${F.vscodeFileResource}://${Y1.a}`)?t.with({scheme:F.vscodeFileResource,authority:t.authority||Y1.a,query:null,fragment:null}):t}asFileUri(t){const r=this.b(t);return this.uriToFileUri(r)}uriToFileUri(t){return t.scheme===F.vscodeFileResource?t.with({scheme:F.file,authority:t.authority!==Y1.a?t.authority:null,query:null,fragment:null}):t}b(t){if(W.isUri(t))return t;if(globalThis._VSCODE_FILE_ROOT){const r=globalThis._VSCODE_FILE_ROOT;if(/^\w[\w\d+.-]*:\/\//.test(r))return W.joinPath(W.parse(r,!0),t);const n=yn(r,t);return W.file(n)}throw new Error("Cannot determine URI for module id!")}},B2=new qn,H2=Object.freeze({"Cache-Control":"no-cache, no-store"}),V2=Object.freeze({"Document-Policy":"include-js-call-stacks-in-crash-reports"}),wt;(function(e){const t=new Map([["1",{"Cross-Origin-Opener-Policy":"same-origin"}],["2",{"Cross-Origin-Embedder-Policy":"require-corp"}],["3",{"Cross-Origin-Opener-Policy":"same-origin","Cross-Origin-Embedder-Policy":"require-corp"}]]);e.CoopAndCoep=Object.freeze(t.get("3"));const r="vscode-coi";function n(s){let l;typeof s=="string"?l=new URL(s).searchParams:s instanceof URL?l=s.searchParams:W.isUri(s)&&(l=new URL(s.toString(!0)).searchParams);const o=l?.get(r);if(o)return t.get(o)}e.getHeadersFromQuery=n;function i(s,l,o){if(!globalThis.crossOriginIsolated)return;const c=l&&o?"3":o?"2":"1";s instanceof URLSearchParams?s.set(r,c):s[r]=c}e.addSearchParam=i})(wt||(wt={}));function s1(e){return F1(e,!0)}var L1=class{constructor(e){this.a=e}compare(e,t,r=!1){return e===t?0:Hr(this.getComparisonKey(e,r),this.getComparisonKey(t,r))}isEqual(e,t,r=!1){return e===t?!0:!e||!t?!1:this.getComparisonKey(e,r)===this.getComparisonKey(t,r)}getComparisonKey(e,t=!1){return e.with({path:this.a(e)?e.path.toLowerCase():void 0,fragment:t?null:void 0}).toString()}ignorePathCasing(e){return this.a(e)}isEqualOrParent(e,t,r=!1){if(e.scheme===t.scheme){if(e.scheme===F.file)return be(s1(e),s1(t),this.a(e))&&e.query===t.query&&(r||e.fragment===t.fragment);if(bt(e.authority,t.authority))return be(e.path,t.path,this.a(e),"/")&&e.query===t.query&&(r||e.fragment===t.fragment)}return!1}joinPath(e,...t){return W.joinPath(e,...t)}basenameOrAuthority(e){return Fn(e)||e.authority}basename(e){return S.basename(e.path)}extname(e){return S.extname(e.path)}dirname(e){if(e.path.length===0)return e;let t;return e.scheme===F.file?t=W.file(Ln(s1(e))).path:(t=S.dirname(e.path),e.authority&&t.length&&t.charCodeAt(0)!==47&&(console.error(`dirname("${e.toString})) resulted in a relative path`),t="/")),e.with({path:t})}normalizePath(e){if(!e.path.length)return e;let t;return e.scheme===F.file?t=W.file(bn(s1(e))).path:t=S.normalize(e.path),e.with({path:t})}relativePath(e,t){if(e.scheme!==t.scheme||!bt(e.authority,t.authority))return;if(e.scheme===F.file){const i=ut(s1(e),s1(t));return g1?ft(i):i}let r=e.path||"/";const n=t.path||"/";if(this.a(e)){let i=0;for(const s=Math.min(r.length,n.length);i<s&&!(r.charCodeAt(i)!==n.charCodeAt(i)&&r.charAt(i).toLowerCase()!==n.charAt(i).toLowerCase());i++);r=n.substr(0,i)+r.substr(i)}return S.relative(r,n)}resolvePath(e,t){if(e.scheme===F.file){const r=W.file(Cn(s1(e),t));return e.with({authority:r.authority,path:r.path})}return t=En(t),e.with({path:S.resolve(e.path,t)})}isAbsolutePath(e){return!!e.path&&e.path[0]==="/"}isEqualAuthority(e,t){return e===t||e!==void 0&&t!==void 0&&Qr(e,t)}hasTrailingPathSeparator(e,t=l1){if(e.scheme===F.file){const r=s1(e);return r.length>ht(r).length&&r[r.length-1]===t}else{const r=e.path;return r.length>1&&r.charCodeAt(r.length-1)===47&&!/^[a-zA-Z]:(\/$|\\$)/.test(e.fsPath)}}removeTrailingPathSeparator(e,t=l1){return yt(e,t)?e.with({path:e.path.substr(0,e.path.length-1)}):e}addTrailingPathSeparator(e,t=l1){let r=!1;if(e.scheme===F.file){const n=s1(e);r=n!==void 0&&n.length===ht(n).length&&n[n.length-1]===t}else{t="/";const n=e.path;r=n.length===1&&n.charCodeAt(n.length-1)===47}return!r&&!yt(e,t)?e.with({path:e.path+"/"}):e}},k=new L1(()=>!1),Q2=new L1(e=>e.scheme===F.file?!Je:!0),K2=new L1(e=>!0),J2=k.isEqual.bind(k),Z2=k.isEqualOrParent.bind(k),G2=k.getComparisonKey.bind(k),Y2=k.basenameOrAuthority.bind(k),Fn=k.basename.bind(k),X2=k.extname.bind(k),e0=k.dirname.bind(k),t0=k.joinPath.bind(k),r0=k.normalizePath.bind(k),n0=k.relativePath.bind(k),i0=k.resolvePath.bind(k),s0=k.isAbsolutePath.bind(k),bt=k.isEqualAuthority.bind(k),yt=k.hasTrailingPathSeparator.bind(k),o0=k.removeTrailingPathSeparator.bind(k),l0=k.addTrailingPathSeparator.bind(k),Ct;(function(e){e.META_DATA_LABEL="label",e.META_DATA_DESCRIPTION="description",e.META_DATA_SIZE="size",e.META_DATA_MIME="mime";function t(r){const n=new Map;r.path.substring(r.path.indexOf(";")+1,r.path.lastIndexOf(";")).split(";").forEach(l=>{const[o,c]=l.split(":");o&&c&&n.set(o,c)});const s=r.path.substring(0,r.path.indexOf(";"));return s&&n.set(e.META_DATA_MIME,s),n}e.parseMetaData=t})(Ct||(Ct={}));var a0=Symbol("MicrotaskDelay");function Ce(e){return!!e&&typeof e.then=="function"}var Wn,Le;(function(){typeof globalThis.requestIdleCallback!="function"||typeof globalThis.cancelIdleCallback!="function"?Le=(e,t,r)=>{jr(()=>{if(n)return;const i=Date.now()+15;t(Object.freeze({didTimeout:!0,timeRemaining(){return Math.max(0,i-Date.now())}}))});let n=!1;return{dispose(){n||(n=!0)}}}:Le=(e,t,r)=>{const n=e.requestIdleCallback(t,typeof r=="number"?{timeout:r}:void 0);let i=!1;return{dispose(){i||(i=!0,e.cancelIdleCallback(n))}}},Wn=(e,t)=>Le(globalThis,e,t)})();var Lt;(function(e){e[e.Resolved=0]="Resolved",e[e.Rejected=1]="Rejected"})(Lt||(Lt={}));var $e;(function(e){async function t(n){let i;const s=await Promise.all(n.map(l=>l.then(o=>o,o=>{i||(i=o)})));if(typeof i<"u")throw i;return s}e.settled=t;function r(n){return new Promise(async(i,s)=>{try{await n(i,s)}catch(l){s(l)}})}e.withAsyncBody=r})($e||($e={}));var $t;(function(e){e[e.Initial=0]="Initial",e[e.DoneOK=1]="DoneOK",e[e.DoneError=2]="DoneError"})($t||($t={}));var c0=class Q{static fromArray(t){return new Q(r=>{r.emitMany(t)})}static fromPromise(t){return new Q(async r=>{r.emitMany(await t)})}static fromPromisesResolveOrder(t){return new Q(async r=>{await Promise.all(t.map(async n=>r.emitOne(await n)))})}static merge(t){return new Q(async r=>{await Promise.all(t.map(async n=>{for await(const i of n)r.emitOne(i)}))})}static{this.EMPTY=Q.fromArray([])}constructor(t,r){this.a=0,this.b=[],this.d=null,this.f=r,this.g=new Y,queueMicrotask(async()=>{const n={emitOne:i=>this.h(i),emitMany:i=>this.j(i),reject:i=>this.l(i)};try{await Promise.resolve(t(n)),this.k()}catch(i){this.l(i)}finally{n.emitOne=void 0,n.emitMany=void 0,n.reject=void 0}})}[Symbol.asyncIterator](){let t=0;return{next:async()=>{do{if(this.a===2)throw this.d;if(t<this.b.length)return{done:!1,value:this.b[t++]};if(this.a===1)return{done:!0,value:void 0};await k1.toPromise(this.g.event)}while(!0)},return:async()=>(this.f?.(),{done:!0,value:void 0})}}static map(t,r){return new Q(async n=>{for await(const i of t)n.emitOne(r(i))})}map(t){return Q.map(this,t)}static filter(t,r){return new Q(async n=>{for await(const i of t)r(i)&&n.emitOne(i)})}filter(t){return Q.filter(this,t)}static coalesce(t){return Q.filter(t,r=>!!r)}coalesce(){return Q.coalesce(this)}static async toPromise(t){const r=[];for await(const n of t)r.push(n);return r}toPromise(){return Q.toPromise(this)}h(t){this.a===0&&(this.b.push(t),this.g.fire())}j(t){this.a===0&&(this.b=this.b.concat(t),this.g.fire())}k(){this.a===0&&(this.a=1,this.g.fire())}l(t){this.a===0&&(this.a=2,this.d=t,this.g.fire())}},z1="**",At="/",B1="[/\\\\]",H1="[^/\\\\]",zn=/\//g;function Et(e,t){switch(e){case 0:return"";case 1:return`${H1}*?`;default:return`(?:${B1}|${H1}+${B1}${t?`|${B1}${H1}+`:""})*?`}}function xt(e,t){if(!e)return[];const r=[];let n=!1,i=!1,s="";for(const l of e){switch(l){case t:if(!n&&!i){r.push(s),s="";continue}break;case"{":n=!0;break;case"}":n=!1;break;case"[":i=!0;break;case"]":i=!1;break}s+=l}return s&&r.push(s),r}function Nt(e){if(!e)return"";let t="";const r=xt(e,At);if(r.every(n=>n===z1))t=".*";else{let n=!1;r.forEach((i,s)=>{if(i===z1){if(n)return;t+=Et(2,s===r.length-1)}else{let l=!1,o="",c=!1,a="";for(const u of i){if(u!=="}"&&l){o+=u;continue}if(c&&(u!=="]"||!a)){let f;u==="-"?f=u:(u==="^"||u==="!")&&!a?f="^":u===At?f="":f=me(u),a+=f;continue}switch(u){case"{":l=!0;continue;case"[":c=!0;continue;case"}":{const h=`(?:${xt(o,",").map(m=>Nt(m)).join("|")})`;t+=h,l=!1,o="";break}case"]":{t+="["+a+"]",c=!1,a="";break}case"?":t+=H1;continue;case"*":t+=Et(1);continue;default:t+=me(u)}}s<r.length-1&&(r[s+1]!==z1||s+2<r.length)&&(t+=B1)}n=i===z1})}return t}var Bn=/^\*\*\/\*\.[\w\.-]+$/,Hn=/^\*\*\/([\w\.-]+)\/?$/,Vn=/^{\*\*\/\*?[\w\.-]+\/?(,\*\*\/\*?[\w\.-]+\/?)*}$/,Qn=/^{\*\*\/\*?[\w\.-]+(\/(\*\*)?)?(,\*\*\/\*?[\w\.-]+(\/(\*\*)?)?)*}$/,Kn=/^\*\*((\/[\w\.-]+)+)\/?$/,Jn=/^([\w\.-]+(\/[\w\.-]+)*)\/?$/,Pt=new lr(1e4),_t=function(){return!1},e1=function(){return null};function Ae(e,t){if(!e)return e1;let r;typeof e!="string"?r=e.pattern:r=e,r=r.trim();const n=`${r}_${!!t.trimForExclusions}`;let i=Pt.get(n);if(i)return St(i,e);let s;return Bn.test(r)?i=Zn(r.substr(4),r):(s=Hn.exec(Ee(r,t)))?i=Gn(s[1],r):(t.trimForExclusions?Qn:Vn).test(r)?i=Yn(r,t):(s=Kn.exec(Ee(r,t)))?i=kt(s[1].substr(1),r,!0):(s=Jn.exec(Ee(r,t)))?i=kt(s[1],r,!1):i=Xn(r),Pt.set(n,i),St(i,e)}function St(e,t){if(typeof t=="string")return e;const r=function(n,i){return be(n,t.base,!Je)?e(zr(n.substr(t.base.length),l1),i):null};return r.allBasenames=e.allBasenames,r.allPaths=e.allPaths,r.basenames=e.basenames,r.patterns=e.patterns,r}function Ee(e,t){return t.trimForExclusions&&e.endsWith("/**")?e.substr(0,e.length-2):e}function Zn(e,t){return function(r,n){return typeof r=="string"&&r.endsWith(e)?t:null}}function Gn(e,t){const r=`/${e}`,n=`\\${e}`,i=function(l,o){return typeof l!="string"?null:o?o===e?t:null:l===e||l.endsWith(r)||l.endsWith(n)?t:null},s=[e];return i.basenames=s,i.patterns=[t],i.allBasenames=s,i}function Yn(e,t){const r=Ot(e.slice(1,-1).split(",").map(o=>Ae(o,t)).filter(o=>o!==e1),e),n=r.length;if(!n)return e1;if(n===1)return r[0];const i=function(o,c){for(let a=0,u=r.length;a<u;a++)if(r[a](o,c))return e;return null},s=r.find(o=>!!o.allBasenames);s&&(i.allBasenames=s.allBasenames);const l=r.reduce((o,c)=>c.allPaths?o.concat(c.allPaths):o,[]);return l.length&&(i.allPaths=l),i}function kt(e,t,r){const n=l1===S.sep,i=n?e:e.replace(zn,l1),s=l1+i,l=S.sep+e;let o;return r?o=function(c,a){return typeof c=="string"&&(c===i||c.endsWith(s)||!n&&(c===e||c.endsWith(l)))?t:null}:o=function(c,a){return typeof c=="string"&&(c===i||!n&&c===e)?t:null},o.allPaths=[(r?"*/":"./")+e],o}function Xn(e){try{const t=new RegExp(`^${Nt(e)}$`);return function(r){return t.lastIndex=0,typeof r=="string"&&t.test(r)?e:null}}catch{return e1}}function V1(e,t,r){return!e||typeof t!="string"?!1:xe(e)(t,void 0,r)}function xe(e,t={}){if(!e)return _t;if(typeof e=="string"||e2(e)){const r=Ae(e,t);if(r===e1)return _t;const n=function(i,s){return!!r(i,s)};return r.allBasenames&&(n.allBasenames=r.allBasenames),r.allPaths&&(n.allPaths=r.allPaths),n}return t2(e,t)}function e2(e){const t=e;return t?typeof t.base=="string"&&typeof t.pattern=="string":!1}function t2(e,t){const r=Ot(Object.getOwnPropertyNames(e).map(o=>r2(o,e[o],t)).filter(o=>o!==e1)),n=r.length;if(!n)return e1;if(!r.some(o=>!!o.requiresSiblings)){if(n===1)return r[0];const o=function(u,f){let h;for(let m=0,v=r.length;m<v;m++){const y=r[m](u,f);if(typeof y=="string")return y;Ce(y)&&(h||(h=[]),h.push(y))}return h?(async()=>{for(const m of h){const v=await m;if(typeof v=="string")return v}return null})():null},c=r.find(u=>!!u.allBasenames);c&&(o.allBasenames=c.allBasenames);const a=r.reduce((u,f)=>f.allPaths?u.concat(f.allPaths):u,[]);return a.length&&(o.allPaths=a),o}const i=function(o,c,a){let u,f;for(let h=0,m=r.length;h<m;h++){const v=r[h];v.requiresSiblings&&a&&(c||(c=$n(o)),u||(u=c.substr(0,c.length-An(o).length)));const y=v(o,c,u,a);if(typeof y=="string")return y;Ce(y)&&(f||(f=[]),f.push(y))}return f?(async()=>{for(const h of f){const m=await h;if(typeof m=="string")return m}return null})():null},s=r.find(o=>!!o.allBasenames);s&&(i.allBasenames=s.allBasenames);const l=r.reduce((o,c)=>c.allPaths?o.concat(c.allPaths):o,[]);return l.length&&(i.allPaths=l),i}function r2(e,t,r){if(t===!1)return e1;const n=Ae(e,r);if(n===e1)return e1;if(typeof t=="boolean")return n;if(t){const i=t.when;if(typeof i=="string"){const s=(l,o,c,a)=>{if(!a||!n(l,o))return null;const u=i.replace("$(basename)",()=>c),f=a(u);return Ce(f)?f.then(h=>h?e:null):f?e:null};return s.requiresSiblings=!0,s}}return n}function Ot(e,t){const r=e.filter(o=>!!o.basenames);if(r.length<2)return e;const n=r.reduce((o,c)=>{const a=c.basenames;return a?o.concat(a):o},[]);let i;if(t){i=[];for(let o=0,c=n.length;o<c;o++)i.push(t)}else i=r.reduce((o,c)=>{const a=c.patterns;return a?o.concat(a):o},[]);const s=function(o,c){if(typeof o!="string")return null;if(!c){let u;for(u=o.length;u>0;u--){const f=o.charCodeAt(u-1);if(f===47||f===92)break}c=o.substr(u)}const a=n.indexOf(c);return a!==-1?i[a]:null};s.basenames=n,s.patterns=i,s.allBasenames=n;const l=e.filter(o=>!o.basenames);return l.push(s),l}var n2=class Oe{static{this.CHANNEL_NAME="localFileSearchWorkerHost"}static getChannel(t){return t.getChannel(Oe.CHANNEL_NAME)}static setChannel(t,r){t.setChannel(Oe.CHANNEL_NAME,r)}},Rt=class h1{constructor(t,r){this.lineNumber=t,this.column=r}with(t=this.lineNumber,r=this.column){return t===this.lineNumber&&r===this.column?this:new h1(t,r)}delta(t=0,r=0){return this.with(Math.max(1,this.lineNumber+t),Math.max(1,this.column+r))}equals(t){return h1.equals(this,t)}static equals(t,r){return!t&&!r?!0:!!t&&!!r&&t.lineNumber===r.lineNumber&&t.column===r.column}isBefore(t){return h1.isBefore(this,t)}static isBefore(t,r){return t.lineNumber<r.lineNumber?!0:r.lineNumber<t.lineNumber?!1:t.column<r.column}isBeforeOrEqual(t){return h1.isBeforeOrEqual(this,t)}static isBeforeOrEqual(t,r){return t.lineNumber<r.lineNumber?!0:r.lineNumber<t.lineNumber?!1:t.column<=r.column}static compare(t,r){const n=t.lineNumber|0,i=r.lineNumber|0;if(n===i){const s=t.column|0,l=r.column|0;return s-l}return n-i}clone(){return new h1(this.lineNumber,this.column)}toString(){return"("+this.lineNumber+","+this.column+")"}static lift(t){return new h1(t.lineNumber,t.column)}static isIPosition(t){return t&&typeof t.lineNumber=="number"&&typeof t.column=="number"}toJSON(){return{lineNumber:this.lineNumber,column:this.column}}},Tt=class M{constructor(t,r,n,i){t>n||t===n&&r>i?(this.startLineNumber=n,this.startColumn=i,this.endLineNumber=t,this.endColumn=r):(this.startLineNumber=t,this.startColumn=r,this.endLineNumber=n,this.endColumn=i)}isEmpty(){return M.isEmpty(this)}static isEmpty(t){return t.startLineNumber===t.endLineNumber&&t.startColumn===t.endColumn}containsPosition(t){return M.containsPosition(this,t)}static containsPosition(t,r){return!(r.lineNumber<t.startLineNumber||r.lineNumber>t.endLineNumber||r.lineNumber===t.startLineNumber&&r.column<t.startColumn||r.lineNumber===t.endLineNumber&&r.column>t.endColumn)}static strictContainsPosition(t,r){return!(r.lineNumber<t.startLineNumber||r.lineNumber>t.endLineNumber||r.lineNumber===t.startLineNumber&&r.column<=t.startColumn||r.lineNumber===t.endLineNumber&&r.column>=t.endColumn)}containsRange(t){return M.containsRange(this,t)}static containsRange(t,r){return!(r.startLineNumber<t.startLineNumber||r.endLineNumber<t.startLineNumber||r.startLineNumber>t.endLineNumber||r.endLineNumber>t.endLineNumber||r.startLineNumber===t.startLineNumber&&r.startColumn<t.startColumn||r.endLineNumber===t.endLineNumber&&r.endColumn>t.endColumn)}strictContainsRange(t){return M.strictContainsRange(this,t)}static strictContainsRange(t,r){return!(r.startLineNumber<t.startLineNumber||r.endLineNumber<t.startLineNumber||r.startLineNumber>t.endLineNumber||r.endLineNumber>t.endLineNumber||r.startLineNumber===t.startLineNumber&&r.startColumn<=t.startColumn||r.endLineNumber===t.endLineNumber&&r.endColumn>=t.endColumn)}plusRange(t){return M.plusRange(this,t)}static plusRange(t,r){let n,i,s,l;return r.startLineNumber<t.startLineNumber?(n=r.startLineNumber,i=r.startColumn):r.startLineNumber===t.startLineNumber?(n=r.startLineNumber,i=Math.min(r.startColumn,t.startColumn)):(n=t.startLineNumber,i=t.startColumn),r.endLineNumber>t.endLineNumber?(s=r.endLineNumber,l=r.endColumn):r.endLineNumber===t.endLineNumber?(s=r.endLineNumber,l=Math.max(r.endColumn,t.endColumn)):(s=t.endLineNumber,l=t.endColumn),new M(n,i,s,l)}intersectRanges(t){return M.intersectRanges(this,t)}static intersectRanges(t,r){let n=t.startLineNumber,i=t.startColumn,s=t.endLineNumber,l=t.endColumn;const o=r.startLineNumber,c=r.startColumn,a=r.endLineNumber,u=r.endColumn;return n<o?(n=o,i=c):n===o&&(i=Math.max(i,c)),s>a?(s=a,l=u):s===a&&(l=Math.min(l,u)),n>s||n===s&&i>l?null:new M(n,i,s,l)}equalsRange(t){return M.equalsRange(this,t)}static equalsRange(t,r){return!t&&!r?!0:!!t&&!!r&&t.startLineNumber===r.startLineNumber&&t.startColumn===r.startColumn&&t.endLineNumber===r.endLineNumber&&t.endColumn===r.endColumn}getEndPosition(){return M.getEndPosition(this)}static getEndPosition(t){return new Rt(t.endLineNumber,t.endColumn)}getStartPosition(){return M.getStartPosition(this)}static getStartPosition(t){return new Rt(t.startLineNumber,t.startColumn)}toString(){return"["+this.startLineNumber+","+this.startColumn+" -> "+this.endLineNumber+","+this.endColumn+"]"}setEndPosition(t,r){return new M(this.startLineNumber,this.startColumn,t,r)}setStartPosition(t,r){return new M(t,r,this.endLineNumber,this.endColumn)}collapseToStart(){return M.collapseToStart(this)}static collapseToStart(t){return new M(t.startLineNumber,t.startColumn,t.startLineNumber,t.startColumn)}collapseToEnd(){return M.collapseToEnd(this)}static collapseToEnd(t){return new M(t.endLineNumber,t.endColumn,t.endLineNumber,t.endColumn)}delta(t){return new M(this.startLineNumber+t,this.startColumn,this.endLineNumber+t,this.endColumn)}isSingleLine(){return this.startLineNumber===this.endLineNumber}static fromPositions(t,r=t){return new M(t.lineNumber,t.column,r.lineNumber,r.column)}static lift(t){return t?new M(t.startLineNumber,t.startColumn,t.endLineNumber,t.endColumn):null}static isIRange(t){return t&&typeof t.startLineNumber=="number"&&typeof t.startColumn=="number"&&typeof t.endLineNumber=="number"&&typeof t.endColumn=="number"}static areIntersectingOrTouching(t,r){return!(t.endLineNumber<r.startLineNumber||t.endLineNumber===r.startLineNumber&&t.endColumn<r.startColumn||r.endLineNumber<t.startLineNumber||r.endLineNumber===t.startLineNumber&&r.endColumn<t.startColumn)}static areIntersecting(t,r){return!(t.endLineNumber<r.startLineNumber||t.endLineNumber===r.startLineNumber&&t.endColumn<=r.startColumn||r.endLineNumber<t.startLineNumber||r.endLineNumber===t.startLineNumber&&r.endColumn<=t.startColumn)}static areOnlyIntersecting(t,r){return!(t.endLineNumber<r.startLineNumber-1||t.endLineNumber===r.startLineNumber&&t.endColumn<r.startColumn-1||r.endLineNumber<t.startLineNumber-1||r.endLineNumber===t.startLineNumber&&r.endColumn<t.startColumn-1)}static compareRangesUsingStarts(t,r){if(t&&r){const s=t.startLineNumber|0,l=r.startLineNumber|0;if(s===l){const o=t.startColumn|0,c=r.startColumn|0;if(o===c){const a=t.endLineNumber|0,u=r.endLineNumber|0;if(a===u){const f=t.endColumn|0,h=r.endColumn|0;return f-h}return a-u}return o-c}return s-l}return(t?1:0)-(r?1:0)}static compareRangesUsingEnds(t,r){return t.endLineNumber===r.endLineNumber?t.endColumn===r.endColumn?t.startLineNumber===r.startLineNumber?t.startColumn-r.startColumn:t.startLineNumber-r.startLineNumber:t.endColumn-r.endColumn:t.endLineNumber-r.endLineNumber}static spansMultipleLines(t){return t.endLineNumber>t.startLineNumber}toJSON(){return this}},i2=(e,t,r)=>{let n;if(e[0]===255&&e[1]===254)n=new TextDecoder("utf-16le").decode(e);else if(e[0]===254&&e[1]===255)n=new TextDecoder("utf-16be").decode(e);else if(n=new TextDecoder("utf8").decode(e),n.slice(0,1e3).includes("\uFFFD")&&e.includes(0))return[];const i=[],s=[];let l=null,o=r.remainingResultQuota;for(;o>=0&&(l=t.exec(n));)s.push({matchStartIndex:l.index,matchedText:l[0]}),o--;if(s.length){const c=new Set,a=new Set,u=[],f=g=>n.slice(u[g].start,u[g].end);let h=0,m=null;const v=/\r?\n/g;for(;m=v.exec(n);)u.push({start:h,end:m.index}),h=m.index+m[0].length;h<n.length&&u.push({start:h,end:n.length});let y=0;for(const{matchStartIndex:g,matchedText:N}of s){if(o<0)break;for(;u[y+1]&&g>u[y].end;)y++;let A=y;for(;u[A+1]&&g+N.length>u[A].end;)A++;if(r.surroundingContext)for(let O=Math.max(0,y-r.surroundingContext);O<y;O++)c.add(O);let _="",K=0;for(let O=y;O<=A;O++){let j=f(O);r.previewOptions?.charsPerLine&&j.length>r.previewOptions.charsPerLine&&(K=Math.max(g-u[y].start-20,0),j=j.substr(K,r.previewOptions.charsPerLine)),_+=`${j}
`,a.add(O)}const C=new Tt(y,g-u[y].start,A,g+N.length-u[A].start),$=new Tt(0,g-u[y].start-K,A-y,g+N.length-u[A].start-(A===y?K:0)),P={rangeLocations:[{source:C,preview:$}],previewText:_};if(i.push(P),r.surroundingContext)for(let O=A+1;O<=Math.min(A+r.surroundingContext,u.length-1);O++)c.add(O)}for(const g of c)a.has(g)||i.push({text:f(g),lineNumber:g+1})}return i},s2=class{constructor(e,t,r){if(this.b=t,this.c=r,t[t.length-1]==="\\")throw Error("Unexpected path format, do not use trailing backslashes");t[t.length-1]!=="/"&&(t+="/"),this.a=this.e(e,this.b,this.c)}updateContents(e){this.a=this.e(e,this.b,this.c)}isPathIncludedInTraversal(e,t){if(e[0]!=="/"||e[e.length-1]==="/")throw Error("Unexpected path format, expectred to begin with slash and end without. got:"+e);return!this.a(e,t)}isArbitraryPathIgnored(e,t){if(e[0]!=="/"||e[e.length-1]==="/")throw Error("Unexpected path format, expectred to begin with slash and end without. got:"+e);const r=e.split("/").filter(s=>s);let n=!1,i="";for(let s=0;s<r.length;s++){const l=s===r.length-1,o=r[s];if(i=i+"/"+o,!this.isPathIncludedInTraversal(i,l?t:!0)){n=!0;break}}return n}d(e,t,r){const n=e.map(s=>this.f(s,t)),i=Object.create(null);for(const s of n)i[s]=!0;return xe(i,{trimForExclusions:r})}e(e,t,r){const n=e.split(`
`).map(v=>v.trim()).filter(v=>v&&v[0]!=="#"),i=n.filter(v=>!v.endsWith("/")),s=i.filter(v=>!v.includes("!")),l=this.d(s,t,!0),o=i.filter(v=>v.includes("!")).map(v=>v.replace(/!/g,"")),c=this.d(o,t,!1),a=n.filter(v=>!v.includes("!")),u=this.d(a,t,!0),f=n.filter(v=>v.includes("!")).map(v=>v.replace(/!/g,"")),h=this.d(f,t,!1);return(v,y)=>v.startsWith(t)?y&&u(v)&&!h(v)||l(v)&&!c(v)?!0:r?r.a(v,y):!1:!1}f(e,t){const r=e.indexOf("/");return r===-1||r===e.length-1?e="**/"+e:(r===0?t.slice(-1)==="/"&&(e=e.slice(1)):t.slice(-1)!=="/"&&(e="/"+e),e=t+e),e}},Q1=typeof Buffer<"u",o2=new de(()=>new Uint8Array(256)),Ne,Pe,l2=class J{static alloc(t){return Q1?new J(Buffer.allocUnsafe(t)):new J(new Uint8Array(t))}static wrap(t){return Q1&&!Buffer.isBuffer(t)&&(t=Buffer.from(t.buffer,t.byteOffset,t.byteLength)),new J(t)}static fromString(t,r){return!(r?.dontUseNodeBuffer||!1)&&Q1?new J(Buffer.from(t)):(Ne||(Ne=new TextEncoder),new J(Ne.encode(t)))}static fromByteArray(t){const r=J.alloc(t.length);for(let n=0,i=t.length;n<i;n++)r.buffer[n]=t[n];return r}static concat(t,r){if(typeof r>"u"){r=0;for(let s=0,l=t.length;s<l;s++)r+=t[s].byteLength}const n=J.alloc(r);let i=0;for(let s=0,l=t.length;s<l;s++){const o=t[s];n.set(o,i),i+=o.byteLength}return n}constructor(t){this.buffer=t,this.byteLength=this.buffer.byteLength}clone(){const t=J.alloc(this.byteLength);return t.set(this),t}toString(){return Q1?this.buffer.toString():(Pe||(Pe=new TextDecoder),Pe.decode(this.buffer))}slice(t,r){return new J(this.buffer.subarray(t,r))}set(t,r){if(t instanceof J)this.buffer.set(t.buffer,r);else if(t instanceof Uint8Array)this.buffer.set(t,r);else if(t instanceof ArrayBuffer)this.buffer.set(new Uint8Array(t),r);else if(ArrayBuffer.isView(t))this.buffer.set(new Uint8Array(t.buffer,t.byteOffset,t.byteLength),r);else throw new Error("Unknown argument 'array'")}readUInt32BE(t){return c2(this.buffer,t)}writeUInt32BE(t,r){u2(this.buffer,t,r)}readUInt32LE(t){return f2(this.buffer,t)}writeUInt32LE(t,r){h2(this.buffer,t,r)}readUInt8(t){return d2(this.buffer,t)}writeUInt8(t,r){m2(this.buffer,t,r)}indexOf(t,r=0){return a2(this.buffer,t instanceof J?t.buffer:t,r)}equals(t){return this===t?!0:this.byteLength!==t.byteLength?!1:this.buffer.every((r,n)=>r===t.buffer[n])}};function a2(e,t,r=0){const n=t.byteLength,i=e.byteLength;if(n===0)return 0;if(n===1)return e.indexOf(t[0]);if(n>i-r)return-1;const s=o2.value;s.fill(t.length);for(let a=0;a<t.length;a++)s[t[a]]=t.length-a-1;let l=r+t.length-1,o=l,c=-1;for(;l<i;)if(e[l]===t[o]){if(o===0){c=l;break}l--,o--}else l+=Math.max(t.length-o,s[e[l]]),o=t.length-1;return c}function c2(e,t){return e[t]*2**24+e[t+1]*2**16+e[t+2]*2**8+e[t+3]}function u2(e,t,r){e[r+3]=t,t=t>>>8,e[r+2]=t,t=t>>>8,e[r+1]=t,t=t>>>8,e[r]=t}function f2(e,t){return e[t+0]<<0>>>0|e[t+1]<<8>>>0|e[t+2]<<16>>>0|e[t+3]<<24>>>0}function h2(e,t,r){e[r+0]=t&255,t=t>>>8,e[r+1]=t&255,t=t>>>8,e[r+2]=t&255,t=t>>>8,e[r+3]=t&255}function d2(e,t){return e[t]}function m2(e,t,r){e[r]=t}function K1(e,t=0){if(!e||t>200)return e;if(typeof e=="object"){switch(e.$mid){case 1:return W.revive(e);case 2:return new RegExp(e.source,e.flags);case 17:return new Date(e.source)}if(e instanceof l2||e instanceof Uint8Array)return e;if(Array.isArray(e))for(let r=0;r<e.length;++r)e[r]=K1(e[r],t+1);else for(const r in e)Object.hasOwnProperty.call(e,r)&&(e[r]=K1(e[r],t+1))}return e}var It=!1,v2=+new Date,Dt={},w1=async(e,t)=>{if(!It)return t();const r=Date.now(),n=(Dt[e]??0)+1;console.info(e,n,"starting",Math.round((r-v2)*10)/1e4),Dt[e]=n;const i=await t(),s=Date.now();return console.info(e,n,"took",s-r),i};function g2(e){return new p2(e)}var p2=class{constructor(e){this.cancellationTokens=new Map,this.d=n2.getChannel(e)}$cancelQuery(e){this.cancellationTokens.get(e)?.cancel()}g(e){const t=new qr;return this.cancellationTokens.set(e,t),t}async $listDirectory(e,t,r,n,i){const s=_e(r),l=new L1(()=>n),o=this.g(i),c=[];let a=!1,u=0;const f=t.maxResults||512,h=t.filePattern?m=>t.filePattern.split("").every(v=>m.includes(v)):m=>!0;return await w1("listDirectory",()=>this.h(e,Mt(t),s,l,m=>{if(h(m.name))return u++,f&&u>f&&(a=!0,o.cancel()),c.push(m.path)},o.token)),{results:c,limitHit:a}}async $searchDirectory(e,t,r,n,i){const s=_e(r),l=new L1(()=>n);return w1("searchInFiles",async()=>{const o=this.g(i),c=[],a=w2(t.contentPattern),u=[];let f=0,h=0;const m=!1,v=async y=>{if(o.token.isCancellationRequested)return;f++;const g=await y.resolve();if(o.token.isCancellationRequested)return;const N=new Uint8Array(g),A=i2(N,a,{surroundingContext:t.surroundingContext??0,previewOptions:t.previewOptions,remainingResultQuota:t.maxResults?t.maxResults-h:1e4});if(A.length){h+=A.length,t.maxResults&&h>t.maxResults&&o.cancel();const _={resource:W.joinPath(s.folder,y.path),results:A};this.d.$sendTextSearchMatch(_,i),c.push(_)}};return await w1("walkFolderToResolve",()=>this.h(e,Mt(t),s,l,async y=>u.push(v(y)),o.token)),await w1("resolveOngoingProcesses",()=>Promise.all(u)),It&&console.log("Searched in",f,"files"),{results:c,limitHit:m}})}async h(e,t,r,n,i,s){const l=r.excludePattern?.map(g=>xe(g.pattern??{},{trimForExclusions:!0})),o=(g,N,A)=>l?.some(_=>_(g,N,A)),c=(g,N,A)=>(g=g.slice(1),!!(o(g,N,A)||b2(t,g))),a=(g,N,A)=>(g=g.slice(1),!(o(g,N,A)||!y2(t,g,n))),u=(g,N)=>({type:"file",name:g.name,path:N,resolve:()=>g.getFile().then(_=>_.arrayBuffer())}),f=g=>g.kind==="directory",h=g=>g.kind==="file",m=async(g,N,A)=>{if(!r.disregardIgnoreFiles){const K=await Promise.all([g.getFileHandle(".gitignore").catch(C=>{}),g.getFileHandle(".ignore").catch(C=>{})]);await Promise.all(K.map(async C=>{if(!C)return;const $=new TextDecoder("utf8").decode(new Uint8Array(await(await C.getFile()).arrayBuffer()));A=new s2($,N,A)}))}const _=$e.withAsyncBody(async K=>{const C=[],$=[],P=[],O=new Set;for await(const j of g.entries())P.push(j),O.add(j[0]);for(const[j,G]of P){if(s.isCancellationRequested)break;const c1=N+j;if(A&&!A.isPathIncludedInTraversal(c1,G.kind==="directory"))continue;const N1=X1=>O.has(X1);f(G)&&!c(c1,j,N1)?$.push(m(G,c1+"/",A)):h(G)&&a(c1,j,N1)&&C.push(u(G,c1))}K([...await Promise.all($),...C])});return{type:"dir",name:g.name,entries:_}},v=async(g,N)=>{s.isCancellationRequested||await Promise.all((await g.entries).sort((A,_)=>-(A.type==="dir"?0:1)+(_.type==="dir"?0:1)).map(async A=>A.type==="dir"?v(A,N):N(A)))},y=await w1("process",()=>m(e,"/"));await w1("resolve",()=>v(y,i))}};function w2(e){return Br(e.pattern,!!e.isRegExp,{wholeWord:e.isWordMatch,global:!0,matchCase:e.isCaseSensitive,multiline:!0,unicode:!0})}function _e(e){return K1({...K1(e),excludePattern:e.excludePattern?.map(t=>({folder:W.revive(t.folder),pattern:t.pattern})),folder:W.revive(e.folder)})}function Mt(e){return{...e,extraFileResources:e.extraFileResources?.map(t=>W.revive(t)),folderQueries:e.folderQueries.map(t=>_e(t))}}function b2(e,t){return!!(e.excludePattern&&V1(e.excludePattern,t))}function y2(e,t,r){return e.excludePattern&&V1(e.excludePattern,t)?!1:e.includePattern||e.usingSearchPaths?e.includePattern&&V1(e.includePattern,t)?!0:e.usingSearchPaths?!!e.folderQueries&&e.folderQueries.some(n=>{const i=n.folder,s=W.file(t);if(r.isEqualOrParent(s,i)){const l=ut(i.path,s.path);return!n.includePattern||!!V1(n.includePattern,l)}else return!1}):!1:!0}an(g2);

//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/6cb10202a0877c8b8d36fff300b7791e33a4d3e7/core/vs/workbench/services/search/worker/localFileSearchMain.js.map
