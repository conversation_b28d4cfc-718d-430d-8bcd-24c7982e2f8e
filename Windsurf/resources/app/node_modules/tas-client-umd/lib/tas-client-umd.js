!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var s=t();for(var r in s)("object"==typeof exports?exports:e)[r]=s[r]}}("undefined"!=typeof self?self:this,(()=>(()=>{"use strict";var e={291:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ExperimentationService=void 0;const r=s(845),i=s(924),o=s(761);class n extends o.ExperimentationServiceAutoPolling{constructor(e){super(e.telemetry,e.filterProviders||[],null!=e.refetchInterval?e.refetchInterval:0,e.assignmentContextTelemetryPropertyName,e.telemetryEventName,e.<PERSON>,e.keyValueStorage),this.options=e,this.invokeInit()}init(){this.featureProviders=[],this.addFeatureProvider(new r.TasApiFeatureProvider(new i.HttpClient(this.options.endpoint),this.telemetry,this.filterProviders)),super.init()}}t.ExperimentationService=n,n.REFRESH_RATE_IN_MINUTES=30},761:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ExperimentationServiceAutoPolling=void 0;const r=s(714),i=s(257);class o extends r.ExperimentationServiceBase{constructor(e,t,s,r,o,n,a){if(super(e,r,o,n,a),this.telemetry=e,this.filterProviders=t,this.refreshRateMs=s,this.assignmentContextTelemetryPropertyName=r,this.telemetryEventName=o,this.storageKey=n,this.storage=a,s<1e3&&0!==s)throw new Error("The minimum refresh rate for polling is 1000 ms (1 second). If you wish to deactivate this auto-polling use value of 0.");s>0&&(this.pollingService=new i.PollingService(s),this.pollingService.OnPollTick((async()=>{await super.getFeaturesAsync()})))}init(){this.pollingService?this.pollingService.StartPolling(!0):super.getFeaturesAsync()}async getFeaturesAsync(e=!1){if(this.pollingService){this.pollingService.StopPolling();let t=await super.getFeaturesAsync(e);return this.pollingService.StartPolling(),t}return await super.getFeaturesAsync(e)}}t.ExperimentationServiceAutoPolling=o},714:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ExperimentationServiceBase=void 0;const r=s(823);t.ExperimentationServiceBase=class{get features(){return this._features}set features(e){this._features=e,this.telemetry&&this.telemetry.setSharedProperty(this.assignmentContextTelemetryPropertyName,this.features.assignmentContext)}constructor(e,t,s,i,o){this.telemetry=e,this.assignmentContextTelemetryPropertyName=t,this.telemetryEventName=s,this.storageKey=i,this.storage=o,this.featuresConsumed=!1,this.cachedTelemetryEvents=[],this._features={features:[],assignmentContext:"",configs:[]},this.storageKey||(this.storageKey="ABExp.Features"),this.storage||(o=new r.MemoryKeyValueStorage),this.loadCachePromise=this.loadCachedFeatureData(),this.initializePromise=this.loadCachePromise,this.initialFetch=new Promise(((e,t)=>{this.resolveInitialFetchPromise=e}))}async getFeaturesAsync(e=!1){if(null!=this.fetchPromise){try{await this.fetchPromise}catch(e){}return this.features}if(!this.featureProviders||0===this.featureProviders.length)return Promise.resolve({features:[],assignmentContext:"",configs:[]});try{this.fetchPromise=Promise.all(this.featureProviders.map((async e=>await e.getFeatures())));const t=await this.fetchPromise;this.updateFeatures(t,e)}catch(e){}return this.fetchPromise=void 0,this.resolveInitialFetchPromise&&(this.resolveInitialFetchPromise(),this.resolveInitialFetchPromise=void 0),this.features}updateFeatures(e,t=!1){let s={features:[],assignmentContext:"",configs:[]};for(let t of e){for(let e of t.features)s.features.includes(e)||s.features.push(e);for(let e of t.configs){const t=s.configs.find((t=>t.Id===e.Id));t?t.Parameters=Object.assign(Object.assign({},t.Parameters),e.Parameters):s.configs.push(e)}s.assignmentContext+=t.assignmentContext}!t&&this.featuresConsumed||(this.features=s),this.storage&&this.storage.setValue(this.storageKey,s)}async loadCachedFeatureData(){let e;this.storage&&(e=await this.storage.getValue(this.storageKey),void 0!==e&&void 0===e.configs&&(e.configs=[])),0===this.features.features.length&&(this.features=e||{features:[],assignmentContext:"",configs:[]})}isFlightEnabled(e){return this.featuresConsumed=!0,this.PostEventToTelemetry(e),this.features.features.includes(e)}async isCachedFlightEnabled(e){return await this.loadCachePromise,this.featuresConsumed=!0,this.PostEventToTelemetry(e),this.features.features.includes(e)}async isFlightEnabledAsync(e){const t=await this.getFeaturesAsync(!0);return this.featuresConsumed=!0,this.PostEventToTelemetry(e),t.features.includes(e)}getTreatmentVariable(e,t){this.featuresConsumed=!0,this.PostEventToTelemetry(`${e}.${t}`);const s=this.features.configs.find((t=>t.Id===e));return null==s?void 0:s.Parameters[t]}async getTreatmentVariableAsync(e,t,s){if(s){const s=this.featuresConsumed,r=this.getTreatmentVariable(e,t);if(void 0!==r)return r;this.featuresConsumed=s}return await this.getFeaturesAsync(!0),this.getTreatmentVariable(e,t)}PostEventToTelemetry(e){this.cachedTelemetryEvents.includes(e)||(this.telemetry.postEvent(this.telemetryEventName,new Map([["ABExp.queriedFeature",e]])),this.cachedTelemetryEvents.push(e))}invokeInit(){this.init()}addFeatureProvider(...e){if(null!=e&&null!=this.featureProviders)for(let t of e)this.featureProviders.push(t)}}},790:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaseFeatureProvider=void 0,t.BaseFeatureProvider=class{constructor(e){this.telemetry=e,this.isFetching=!1}async getFeatures(){if(this.isFetching&&this.fetchPromise)return this.fetchPromise;this.fetchPromise=this.fetch();let e=await this.fetchPromise;return this.isFetching=!1,this.fetchPromise=void 0,e}}},492:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.FilteredFeatureProvider=void 0;const r=s(790);class i extends r.BaseFeatureProvider{constructor(e,t){super(e),this.telemetry=e,this.filterProviders=t,this.cachedTelemetryEvents=[]}getFilters(){let e=new Map;for(let t of this.filterProviders){let s=t.getFilters();for(let t of s.keys()){let r=s.get(t);e.set(t,r)}}return e}PostEventToTelemetry(e){if(this.cachedTelemetryEvents.includes(e))return;const t=JSON.stringify(e);this.telemetry.postEvent("report-headers",new Map([["ABExp.headers",t]])),this.cachedTelemetryEvents.push(e)}}t.FilteredFeatureProvider=i},845:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TasApiFeatureProvider=t.TASAPI_FETCHERROR_EVENTNAME=void 0;const r=s(492);t.TASAPI_FETCHERROR_EVENTNAME="call-tas-error";const i="ErrorType";class o extends r.FilteredFeatureProvider{constructor(e,t,s){super(t,s),this.httpClient=e,this.telemetry=t,this.filterProviders=s}async fetch(){let e,s=this.getFilters(),r={};for(let e of s.keys()){const t=s.get(e);r[e]=t}try{e=await this.httpClient.get({headers:r})}catch(e){const s=e,r=new Map;s.responseReceived&&!s.responseOk?r.set(i,"ServerError"):!1===s.responseReceived?r.set(i,"NoResponse"):r.set(i,"GenericError"),this.telemetry.postEvent(t.TASAPI_FETCHERROR_EVENTNAME,r)}if(!e)throw Error(t.TASAPI_FETCHERROR_EVENTNAME);s.keys.length>0&&this.PostEventToTelemetry(r);const o=e.data;let n=o.Configs,a=[];for(let e of n)if(e.Parameters)for(let t of Object.keys(e.Parameters)){const s=t+(e.Parameters[t]?"":"cf");a.includes(s)||a.push(s)}return{features:a,assignmentContext:o.AssignmentContext,configs:n}}}t.TasApiFeatureProvider=o},924:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.HttpClient=t.FetchError=void 0;class s extends Error{constructor(e,t,s){super(e),this.responseReceived=t,this.responseOk=s}}t.FetchError=s,t.HttpClient=class{constructor(e){this.endpoint=e}async get(e){const t=await fetch(this.endpoint,{method:"GET",headers:null==e?void 0:e.headers});if(!t)throw new s("No response received",!1);if(!t.ok)throw new s("Response not ok",!0,!1);const r=await t.json();if(!r)throw new s("No data received",!1);return{data:r}}}},823:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MemoryKeyValueStorage=void 0,t.MemoryKeyValueStorage=class{constructor(){this.storage=new Map}async getValue(e,t){return this.storage.has(e)?await Promise.resolve(this.storage.get(e)):await Promise.resolve(t||void 0)}setValue(e,t){this.storage.set(e,t)}}},257:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PollingService=void 0,t.PollingService=class{constructor(e){this.fetchInterval=e}StopPolling(){clearInterval(this.intervalHandle),this.intervalHandle=void 0}OnPollTick(e){this.onTick=e}StartPolling(e=!1){this.intervalHandle&&this.StopPolling(),null!=this.onTick&&(e&&this.onTick().then((()=>{})).catch((()=>{})),this.intervalHandle=setInterval((async()=>{await this.onTick()}),this.fetchInterval),this.intervalHandle.unref&&this.intervalHandle.unref())}}}},t={};function s(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,s),o.exports}var r={};return(()=>{var e=r;Object.defineProperty(e,"__esModule",{value:!0}),e.ExperimentationService=void 0;var t=s(291);Object.defineProperty(e,"ExperimentationService",{enumerable:!0,get:function(){return t.ExperimentationService}})})(),r})()));//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/6cb10202a0877c8b8d36fff300b7791e33a4d3e7/node_modules/tas-client-umd/lib/tas-client-umd.js.map