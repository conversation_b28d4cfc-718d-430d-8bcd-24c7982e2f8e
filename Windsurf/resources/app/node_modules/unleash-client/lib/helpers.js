"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createFallbackFunction = createFallbackFunction;
exports.resolveContextValue = resolveContextValue;
exports.safeName = safeName;
exports.generateInstanceId = generateInstanceId;
exports.generateHashOfConfig = generateHashOfConfig;
exports.getAppliedJitter = getAppliedJitter;
const os_1 = require("os");
const murmurHash3 = require("murmurhash3js");
function createFallbackFunction(name, context, fallback) {
    if (typeof fallback === 'function') {
        return () => fallback(name, context);
    }
    if (typeof fallback === 'boolean') {
        return () => fallback;
    }
    return () => false;
}
function resolveContextValue(context, field) {
    var _a, _b;
    const contextValue = (_a = context[field]) !== null && _a !== void 0 ? _a : (_b = context.properties) === null || _b === void 0 ? void 0 : _b[field];
    return contextValue !== undefined && contextValue !== null ? String(contextValue) : undefined;
}
function safeName(str = '') {
    return str.replace(/\//g, '_');
}
function generateInstanceId(instanceId) {
    if (instanceId) {
        return instanceId;
    }
    let info;
    try {
        info = (0, os_1.userInfo)();
    }
    catch (e) {
        // unable to read info;
    }
    const prefix = info
        ? info.username
        : `generated-${Math.round(Math.random() * 1000000)}-${process.pid}`;
    return `${prefix}-${(0, os_1.hostname)()}`;
}
function generateHashOfConfig(o) {
    const oAsString = JSON.stringify(o);
    return murmurHash3.x86.hash128(oAsString);
}
function getAppliedJitter(jitter) {
    const appliedJitter = Math.random() * jitter;
    return Math.random() < 0.5 ? -appliedJitter : appliedJitter;
}//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/6cb10202a0877c8b8d36fff300b7791e33a4d3e7/node_modules/unleash-client/lib/helpers.js.map