"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.get = exports.post = exports.buildHeaders = exports.getDefaultAgent = void 0;
const fetch = require("make-fetch-happen");
const http_proxy_agent_1 = require("http-proxy-agent");
const https_proxy_agent_1 = require("https-proxy-agent");
const http = require("http");
const https = require("https");
const httpProxy = process.env.HTTP_PROXY || process.env.http_proxy;
const httpsProxy = process.env.HTTPS_PROXY || process.env.https_proxy;
const httpAgentOptions = {
    keepAlive: true,
    keepAliveMsecs: 30 * 1000,
    timeout: 10 * 1000,
};
const httpAgent = httpProxy
    ? new http_proxy_agent_1.HttpProxyAgent(httpProxy, httpAgentOptions)
    : new http.Agent(httpAgentOptions);
const httpsAgent = httpsProxy
    ? new https_proxy_agent_1.HttpsProxyAgent(httpsProxy, httpAgentOptions)
    : new https.Agent(httpAgentOptions);
const getDefaultAgent = (url) => (url.protocol === 'https:' ? httpsAgent : httpAgent);
exports.getDefaultAgent = getDefaultAgent;
const buildHeaders = (appName, instanceId, etag, contentType, custom, specVersionSupported) => {
    const head = {};
    if (appName) {
        head['UNLEASH-APPNAME'] = appName;
        head['User-Agent'] = appName;
    }
    if (instanceId) {
        head['UNLEASH-INSTANCEID'] = instanceId;
    }
    if (etag) {
        head['If-None-Match'] = etag;
    }
    if (contentType) {
        head['Content-Type'] = contentType;
    }
    if (specVersionSupported) {
        head['Unleash-Client-Spec'] = specVersionSupported;
    }
    if (custom) {
        Object.assign(head, custom);
    }
    return head;
};
exports.buildHeaders = buildHeaders;
const post = ({ url, appName, timeout, instanceId, headers, json, httpOptions, }) => fetch(url, {
    timeout: timeout || 10000,
    method: 'POST',
    agent: (httpOptions === null || httpOptions === void 0 ? void 0 : httpOptions.agent) || exports.getDefaultAgent,
    headers: (0, exports.buildHeaders)(appName, instanceId, undefined, 'application/json', headers),
    body: JSON.stringify(json),
    strictSSL: httpOptions === null || httpOptions === void 0 ? void 0 : httpOptions.rejectUnauthorized,
});
exports.post = post;
const get = ({ url, etag, appName, timeout, instanceId, headers, httpOptions, supportedSpecVersion, }) => fetch(url, {
    method: 'GET',
    timeout: timeout || 10000,
    agent: (httpOptions === null || httpOptions === void 0 ? void 0 : httpOptions.agent) || exports.getDefaultAgent,
    headers: (0, exports.buildHeaders)(appName, instanceId, etag, undefined, headers, supportedSpecVersion),
    retry: {
        retries: 2,
        maxTimeout: timeout || 10000,
    },
    strictSSL: httpOptions === null || httpOptions === void 0 ? void 0 : httpOptions.rejectUnauthorized,
});
exports.get = get;//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/6cb10202a0877c8b8d36fff300b7791e33a4d3e7/node_modules/unleash-client/lib/request.js.map