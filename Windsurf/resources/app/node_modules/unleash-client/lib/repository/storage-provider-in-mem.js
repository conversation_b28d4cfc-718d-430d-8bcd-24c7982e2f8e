"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
class InMemStorageProvider {
    constructor() {
        this.store = new Map();
    }
    async set(key, data) {
        this.store.set(key, data);
        return Promise.resolve();
    }
    async get(key) {
        return Promise.resolve(this.store.get(key));
    }
}
exports.default = InMemStorageProvider;//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/6cb10202a0877c8b8d36fff300b7791e33a4d3e7/node_modules/unleash-client/lib/repository/storage-provider-in-mem.js.map