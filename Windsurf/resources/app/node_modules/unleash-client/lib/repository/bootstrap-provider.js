"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DefaultBootstrapProvider = void 0;
exports.resolveBootstrapProvider = resolveBootstrapProvider;
const fs_1 = require("fs");
const fetch = require("make-fetch-happen");
const request_1 = require("../request");
class DefaultBootstrapProvider {
    constructor(options, appName, instanceId) {
        this.url = options.url;
        this.urlHeaders = options.urlHeaders;
        this.filePath = options.filePath;
        this.data = options.data;
        this.segments = options.segments;
        this.appName = appName;
        this.instanceId = instanceId;
    }
    async loadFromUrl(bootstrapUrl) {
        const response = await fetch(bootstrapUrl, {
            method: 'GET',
            timeout: 10000,
            headers: (0, request_1.buildHeaders)(this.appName, this.instanceId, undefined, undefined, this.urlHeaders),
            retry: {
                retries: 2,
                maxTimeout: 10000,
            },
        });
        if (response.ok) {
            return response.json();
        }
        return undefined;
    }
    async loadFromFile(filePath) {
        const fileContent = await fs_1.promises.readFile(filePath, 'utf8');
        return JSON.parse(fileContent);
    }
    async readBootstrap() {
        if (this.data) {
            return { version: 2, segments: this.segments, features: [...this.data] };
        }
        if (this.url) {
            return this.loadFromUrl(this.url);
        }
        if (this.filePath) {
            return this.loadFromFile(this.filePath);
        }
        return undefined;
    }
}
exports.DefaultBootstrapProvider = DefaultBootstrapProvider;
function resolveBootstrapProvider(options, appName, instanceId) {
    return options.bootstrapProvider || new DefaultBootstrapProvider(options, appName, instanceId);
}//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/6cb10202a0877c8b8d36fff300b7791e33a4d3e7/node_modules/unleash-client/lib/repository/bootstrap-provider.js.map