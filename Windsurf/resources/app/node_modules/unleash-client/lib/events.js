"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnleashEvents = void 0;
exports.createImpressionEvent = createImpressionEvent;
// eslint-disable-next-line import/prefer-default-export
var UnleashEvents;
(function (UnleashEvents) {
    UnleashEvents["Ready"] = "ready";
    UnleashEvents["Error"] = "error";
    UnleashEvents["Warn"] = "warn";
    UnleashEvents["Unchanged"] = "unchanged";
    UnleashEvents["Changed"] = "changed";
    UnleashEvents["Synchronized"] = "synchronized";
    UnleashEvents["Count"] = "count";
    UnleashEvents["CountVariant"] = "countVariant";
    UnleashEvents["Sent"] = "sent";
    UnleashEvents["Registered"] = "registered";
    UnleashEvents["Impression"] = "impression";
})(UnleashEvents || (exports.UnleashEvents = UnleashEvents = {}));
// Wrapper to provide type checking.
function createImpressionEvent(evt) {
    return evt;
}//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/6cb10202a0877c8b8d36fff300b7791e33a4d3e7/node_modules/unleash-client/lib/events.js.map