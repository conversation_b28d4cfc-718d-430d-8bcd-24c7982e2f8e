"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.defaultVariant = exports.PayloadType = void 0;
exports.getDefaultVariant = getDefaultVariant;
exports.selectVariantDefinition = selectVariantDefinition;
exports.selectVariant = selectVariant;
const util_1 = require("./strategy/util");
const helpers_1 = require("./helpers");
var PayloadType;
(function (PayloadType) {
    PayloadType["STRING"] = "string";
    PayloadType["JSON"] = "json";
    PayloadType["CSV"] = "csv";
    PayloadType["NUMBER"] = "number";
})(PayloadType || (exports.PayloadType = PayloadType = {}));
exports.defaultVariant = {
    name: 'disabled',
    enabled: false,
    feature_enabled: false,
};
/**
 * @deprecated Use {@link defaultVariant} const instead
 */
function getDefaultVariant() {
    return exports.defaultVariant;
}
function randomString() {
    return String(Math.round(Math.random() * 100000));
}
const stickinessSelectors = ['userId', 'sessionId', 'remoteAddress'];
function getSeed(context, stickiness = 'default') {
    if (stickiness !== 'default') {
        const value = (0, helpers_1.resolveContextValue)(context, stickiness);
        return value ? value.toString() : randomString();
    }
    let result;
    stickinessSelectors.some((key) => {
        const value = context[key];
        if (typeof value === 'string' && value !== '') {
            result = value;
            return true;
        }
        return false;
    });
    return result || randomString();
}
function overrideMatchesContext(context) {
    return (o) => o.values.some((value) => value === (0, helpers_1.resolveContextValue)(context, o.contextName));
}
function findOverride(variants, context) {
    return variants
        .filter((variant) => variant.overrides)
        .find((variant) => { var _a; return (_a = variant.overrides) === null || _a === void 0 ? void 0 : _a.some(overrideMatchesContext(context)); });
}
function selectVariantDefinition(groupId, stickiness, variants, context) {
    const totalWeight = variants.reduce((acc, v) => acc + v.weight, 0);
    if (totalWeight <= 0) {
        return null;
    }
    const variantOverride = findOverride(variants, context);
    if (variantOverride) {
        return variantOverride;
    }
    const target = (0, util_1.normalizedVariantValue)(getSeed(context, stickiness), groupId, totalWeight);
    let counter = 0;
    const variant = variants.find((v) => {
        if (v.weight === 0) {
            return undefined;
        }
        counter += v.weight;
        if (counter < target) {
            return undefined;
        }
        return v;
    });
    return variant || null;
}
function selectVariant(feature, context) {
    var _a, _b, _c;
    const stickiness = (_c = (_b = (_a = feature.variants) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.stickiness) !== null && _c !== void 0 ? _c : undefined;
    return selectVariantDefinition(feature.name, stickiness, feature.variants || [], context);
}//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/6cb10202a0877c8b8d36fff300b7791e33a4d3e7/node_modules/unleash-client/lib/variant.js.map