{"version": 3, "file": "client.d.ts", "sourceRoot": "", "sources": ["../src/client.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,EAAE,QAAQ,EAAE,0BAA0B,EAAE,MAAM,YAAY,CAAC;AAClE,OAAO,EAAE,gBAAgB,EAAE,MAAM,WAAW,CAAC;AAC7C,OAAO,EAAE,mBAAmB,EAAE,MAAM,cAAc,CAAC;AACnD,OAAO,EACL,OAAO,EAEP,wBAAwB,EAGzB,MAAM,WAAW,CAAC;AACnB,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AAO1E,MAAM,CAAC,OAAO,OAAO,aAAc,SAAQ,YAAY;IACrD,OAAO,CAAC,UAAU,CAAsB;IAExC,OAAO,CAAC,UAAU,CAAa;IAE/B,OAAO,CAAC,gBAAgB,CAAa;IAErC,OAAO,CAAC,kBAAkB,CAAa;gBAE3B,UAAU,EAAE,mBAAmB,EAAE,UAAU,EAAE,QAAQ,EAAE;IAmBnE,OAAO,CAAC,WAAW;IAInB,gBAAgB,CACd,eAAe,EAAE,MAAM,EACvB,IAAI,EAAE,MAAM,EACZ,UAAU,EAAE,0BAA0B,EAAE;IAa1C,kBAAkB,CAAC,iBAAiB,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM;IAU1D,2BAA2B,CAAC,OAAO,EAAE,gBAAgB,GAAG,SAAS,EAAE,OAAO,EAAE,OAAO;IA+BnF,SAAS,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,GAAG,OAAO;IAmBtE,gBAAgB,CACd,OAAO,EAAE,gBAAgB,GAAG,SAAS,EACrC,OAAO,EAAE,OAAO,EAChB,QAAQ,EAAE,QAAQ,GACjB,cAAc;IA6ChB,mBAAmB,CAClB,QAAQ,EAAE,0BAA0B,GACnC,gBAAgB,CAAC,UAAU,GAAG,SAAS,CAAC;IAW1C,uBAAuB,CACtB,QAAQ,EAAE,CAAC,OAAO,GAAG,SAAS,CAAC,EAAE,GAChC,gBAAgB,CAAC,UAAU,GAAG,SAAS,CAAC;IAc3C,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,eAAe,CAAC,EAAE,OAAO,GAAG,wBAAwB;IAqB/F,eAAe,CACb,IAAI,EAAE,MAAM,EACZ,OAAO,EAAE,OAAO,EAChB,eAAe,CAAC,EAAE,OAAO,GACxB,wBAAwB;IAK3B,OAAO,CAAC,cAAc;CA2CvB"}