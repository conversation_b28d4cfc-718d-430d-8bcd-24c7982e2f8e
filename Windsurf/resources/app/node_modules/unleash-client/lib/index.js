"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnleashEvents = exports.InMemStorageProvider = exports.Unleash = exports.PayloadType = exports.Strategy = void 0;
exports.initialize = initialize;
exports.startUnleash = startUnleash;
exports.isEnabled = isEnabled;
exports.destroy = destroy;
exports.getFeatureToggleDefinition = getFeatureToggleDefinition;
exports.getFeatureToggleDefinitions = getFeatureToggleDefinitions;
exports.getVariant = getVariant;
exports.forceGetVariant = forceGetVariant;
exports.count = count;
exports.countVariant = countVariant;
exports.flushMetrics = flushMetrics;
exports.destroyWithFlush = destroyWithFlush;
const events_1 = require("events");
const unleash_1 = require("./unleash");
Object.defineProperty(exports, "Unleash", { enumerable: true, get: function () { return unleash_1.Unleash; } });
const variant_1 = require("./variant");
Object.defineProperty(exports, "PayloadType", { enumerable: true, get: function () { return variant_1.PayloadType; } });
const events_2 = require("./events");
Object.defineProperty(exports, "UnleashEvents", { enumerable: true, get: function () { return events_2.UnleashEvents; } });
const storage_provider_in_mem_1 = require("./repository/storage-provider-in-mem");
exports.InMemStorageProvider = storage_provider_in_mem_1.default;
// exports
var index_1 = require("./strategy/index");
Object.defineProperty(exports, "Strategy", { enumerable: true, get: function () { return index_1.Strategy; } });
let instance;
function initialize(options) {
    instance = unleash_1.Unleash.getInstance(options);
    return instance;
}
async function startUnleash(options) {
    const unleash = initialize(options);
    if (!unleash.isSynchronized()) {
        await (0, events_1.once)(unleash, 'synchronized');
    }
    return unleash;
}
function isEnabled(name, context = {}, fallbackValue) {
    return instance ? instance.isEnabled(name, context, fallbackValue) : !!fallbackValue;
}
function destroy() {
    if (instance) {
        instance.destroy();
    }
    instance = undefined;
}
function getFeatureToggleDefinition(toggleName) {
    return instance && instance.getFeatureToggleDefinition(toggleName);
}
function getFeatureToggleDefinitions(withFullSegments = false) {
    return instance && instance.getFeatureToggleDefinitions(withFullSegments);
}
function getVariant(name, context = {}, fallbackVariant) {
    const variant = fallbackVariant || variant_1.defaultVariant;
    return instance ? instance.getVariant(name, context, variant) : variant;
}
function forceGetVariant(name, context = {}, fallbackVariant) {
    const variant = fallbackVariant || variant_1.defaultVariant;
    return instance ? instance.forceGetVariant(name, context, variant) : variant;
}
function count(toggleName, enabled) {
    return instance && instance.count(toggleName, enabled);
}
function countVariant(toggleName, variantName) {
    return instance && instance.countVariant(toggleName, variantName);
}
async function flushMetrics() {
    return instance && instance.flushMetrics();
}
async function destroyWithFlush() {
    return instance && instance.destroyWithFlush();
}//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/6cb10202a0877c8b8d36fff300b7791e33a4d3e7/node_modules/unleash-client/lib/index.js.map