"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.suffixSlash = void 0;
exports.resolveUrl = resolveUrl;
function resolveUrl(from, to) {
    const resolvedUrl = new URL(to, new URL(from, 'resolve://'));
    if (resolvedUrl.protocol === 'resolve:') {
        // `from` is a relative URL.
        const { pathname, search, hash } = resolvedUrl;
        return pathname + search + hash;
    }
    return resolvedUrl.toString();
}
const getUrl = (base, projectName, namePrefix, tags) => {
    const url = resolveUrl(base, './client/features');
    const params = new URLSearchParams();
    if (projectName) {
        params.append('project', projectName);
    }
    if (namePrefix) {
        params.append('namePrefix', namePrefix);
    }
    if (tags) {
        tags.forEach((tag) => params.append('tag', tag));
    }
    if (params.toString().length > 0) {
        return `${url}?${params.toString()}`;
    }
    return url;
};
const suffixSlash = (url) => (url.endsWith('/') ? url : `${url}/`);
exports.suffixSlash = suffixSlash;
exports.default = getUrl;//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/6cb10202a0877c8b8d36fff300b7791e33a4d3e7/node_modules/unleash-client/lib/url-utils.js.map