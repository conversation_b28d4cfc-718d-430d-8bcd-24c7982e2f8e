"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.normalizedStrategyValue = normalizedStrategyValue;
exports.normalizedVariantValue = normalizedVariantValue;
const murmurHash3 = require("murmurhash3js");
function normalizedValue(id, groupId, normalizer, seed = 0) {
    const hash = murmurHash3.x86.hash32(`${groupId}:${id}`, seed);
    return (hash % normalizer) + 1;
}
const STRATEGY_SEED = 0;
function normalizedStrategyValue(id, groupId) {
    return normalizedValue(id, groupId, 100, STRATEGY_SEED);
}
const VARIANT_SEED = 86028157;
function normalizedVariantValue(id, groupId, normalizer) {
    return normalizedValue(id, groupId, normalizer, VARIANT_SEED);
}//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/6cb10202a0877c8b8d36fff300b7791e33a4d3e7/node_modules/unleash-client/lib/strategy/util.js.map