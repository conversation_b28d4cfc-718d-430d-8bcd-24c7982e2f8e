"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const strategy_1 = require("./strategy");
const util_1 = require("./util");
class GradualRolloutUserIdStrategy extends strategy_1.Strategy {
    constructor() {
        super('gradualRolloutUserId');
    }
    isEnabled(parameters, context) {
        const { userId } = context;
        if (!userId) {
            return false;
        }
        const percentage = Number(parameters.percentage);
        const groupId = parameters.groupId || '';
        const normalizedUserId = (0, util_1.normalizedStrategyValue)(userId, groupId);
        return percentage > 0 && normalizedUserId <= percentage;
    }
}
exports.default = GradualRolloutUserIdStrategy;//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/6cb10202a0877c8b8d36fff300b7791e33a4d3e7/node_modules/unleash-client/lib/strategy/gradual-rollout-user-id.js.map