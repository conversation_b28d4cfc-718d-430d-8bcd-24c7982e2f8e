"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.defaultStrategies = exports.Strategy = void 0;
const default_strategy_1 = require("./default-strategy");
const application_hostname_strategy_1 = require("./application-hostname-strategy");
const gradual_rollout_random_1 = require("./gradual-rollout-random");
const gradual_rollout_user_id_1 = require("./gradual-rollout-user-id");
const gradual_rollout_session_id_1 = require("./gradual-rollout-session-id");
const user_with_id_strategy_1 = require("./user-with-id-strategy");
const remote_addresss_strategy_1 = require("./remote-addresss-strategy");
const flexible_rollout_strategy_1 = require("./flexible-rollout-strategy");
var strategy_1 = require("./strategy");
Object.defineProperty(exports, "Strategy", { enumerable: true, get: function () { return strategy_1.Strategy; } });
exports.defaultStrategies = [
    new default_strategy_1.default(),
    new application_hostname_strategy_1.default(),
    new gradual_rollout_random_1.default(),
    new gradual_rollout_user_id_1.default(),
    new gradual_rollout_session_id_1.default(),
    new user_with_id_strategy_1.default(),
    new remote_addresss_strategy_1.default(),
    new flexible_rollout_strategy_1.default(),
];//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/6cb10202a0877c8b8d36fff300b7791e33a4d3e7/node_modules/unleash-client/lib/strategy/index.js.map