"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const strategy_1 = require("./strategy");
class GradualRolloutRandomStrategy extends strategy_1.Strategy {
    constructor(randomGenerator) {
        super('gradualRolloutRandom');
        this.randomGenerator = () => Math.floor(Math.random() * 100) + 1;
        this.randomGenerator = randomGenerator || this.randomGenerator;
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    isEnabled(parameters, context) {
        const percentage = Number(parameters.percentage);
        const random = this.randomGenerator();
        return percentage >= random;
    }
}
exports.default = GradualRolloutRandomStrategy;//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/6cb10202a0877c8b8d36fff300b7791e33a4d3e7/node_modules/unleash-client/lib/strategy/gradual-rollout-random.js.map