"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const strategy_1 = require("./strategy");
class DefaultStrategy extends strategy_1.Strategy {
    constructor() {
        super('default');
    }
    isEnabled() {
        return true;
    }
}
exports.default = DefaultStrategy;//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/6cb10202a0877c8b8d36fff300b7791e33a4d3e7/node_modules/unleash-client/lib/strategy/default-strategy.js.map