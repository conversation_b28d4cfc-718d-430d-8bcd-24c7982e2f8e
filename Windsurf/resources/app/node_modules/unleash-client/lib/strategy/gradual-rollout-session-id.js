"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const strategy_1 = require("./strategy");
const util_1 = require("./util");
class GradualRolloutSessionIdStrategy extends strategy_1.Strategy {
    constructor() {
        super('gradualRolloutSessionId');
    }
    isEnabled(parameters, context) {
        const { sessionId } = context;
        if (!sessionId) {
            return false;
        }
        const percentage = Number(parameters.percentage);
        const groupId = parameters.groupId || '';
        const normalizedId = (0, util_1.normalizedStrategyValue)(sessionId, groupId);
        return percentage > 0 && normalizedId <= percentage;
    }
}
exports.default = GradualRolloutSessionIdStrategy;//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/6cb10202a0877c8b8d36fff300b7791e33a4d3e7/node_modules/unleash-client/lib/strategy/gradual-rollout-session-id.js.map