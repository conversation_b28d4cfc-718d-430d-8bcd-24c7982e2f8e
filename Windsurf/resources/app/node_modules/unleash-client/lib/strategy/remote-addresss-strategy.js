"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const strategy_1 = require("./strategy");
const ip_address_1 = require("ip-address");
class RemoteAddressStrategy extends strategy_1.Strategy {
    constructor() {
        super('remoteAddress');
    }
    isEnabled(parameters, context) {
        if (!parameters.IPs) {
            return false;
        }
        return parameters.IPs.split(/\s*,\s*/).some((range) => {
            if (range === context.remoteAddress) {
                return true;
            }
            try {
                const subnetRange = new ip_address_1.Address4(range);
                const remoteAddress = new ip_address_1.Address4(context.remoteAddress || '');
                return remoteAddress.isInSubnet(subnetRange);
            }
            catch (err) {
                return false;
            }
        });
    }
}
exports.default = RemoteAddressStrategy;//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/6cb10202a0877c8b8d36fff300b7791e33a4d3e7/node_modules/unleash-client/lib/strategy/remote-addresss-strategy.js.map