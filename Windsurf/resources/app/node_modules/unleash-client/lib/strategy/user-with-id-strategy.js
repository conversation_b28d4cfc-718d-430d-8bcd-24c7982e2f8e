"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const strategy_1 = require("./strategy");
class UserWithIdStrategy extends strategy_1.Strategy {
    constructor() {
        super('userWithId');
    }
    isEnabled(parameters, context) {
        const userIdList = parameters.userIds ? parameters.userIds.split(/\s*,\s*/) : [];
        return userIdList.includes(context.userId);
    }
}
exports.default = UserWithIdStrategy;//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/6cb10202a0877c8b8d36fff300b7791e33a4d3e7/node_modules/unleash-client/lib/strategy/user-with-id-strategy.js.map