{"name": "unleash-client", "version": "6.1.1", "description": "Unleash Client for Node", "license": "Apache-2.0", "main": "./lib/index.js", "typings": "./lib/index.d.ts", "scripts": {"version": "./scripts/update-build-details.sh $npm_package_version", "lint": "eslint .", "lint:fix": "eslint . --fix", "pre-commit": "lint-staged", "prebuild": "del-cli --force lib", "build": "tsc -p .", "prepare": "husky install && yarn run build", "test": "cross-env NODE_ENV=test yarn run build && ava", "coverage": "nyc --reporter=lcov yarn test"}, "repository": {"type": "git", "url": "ssh://**************:Unleash/unleash-client-node.git"}, "keywords": ["unleash", "client", "feature toggle"], "bugs": {"url": "https://github.com/Unleash/unleash-client-node/issues"}, "homepage": "https://github.com/Unleash/unleash-client-node", "dependencies": {"http-proxy-agent": "^7.0.2", "https-proxy-agent": "^7.0.5", "ip-address": "^9.0.5", "make-fetch-happen": "^13.0.1", "murmurhash3js": "^3.0.1", "semver": "^7.6.2"}, "engines": {"node": ">=16"}, "files": ["lib", "!lib/test", "examples"], "devDependencies": {"@ava/babel": "^2.0.0", "@ava/typescript": "^4.0.0", "@tsconfig/node12": "^12.0.0", "@types/express": "^4.17.17", "@types/jsbn": "^1.2.33", "@types/make-fetch-happen": "^10.0.4", "@types/murmurhash3js": "^3.0.3", "@types/nock": "^11.1.0", "@types/node": "^20.2.5", "@types/semver": "^7.5.0", "@types/sinon": "^17.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@unleash/client-specification": "^5.1.6", "ava": "^5.3.0", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "del-cli": "^5.0.0", "eslint": "^8.44.0", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-prettier": "^8.8.0", "eslint-import-resolver-typescript": "^3.5.5", "eslint-plugin-import": "^2.27.5", "eslint-plugin-prettier": "^5.0.0", "esm": "^3.2.25", "husky": "^8.0.3", "lint-staged": "^14.0.0", "mkdirp": "^3.0.1", "nock": "^13.3.1", "nyc": "^15.1.0", "prettier": "^3.0.0", "redis": "^4.6.7", "sinon": "^18.0.0", "typescript": "^5.4.3"}, "resolutions": {"ansi-regex": "^5.0.1", "debug": "^4.0.0", "braces": "^3.0.3", "ip-address": "^9.0.5", "json-schema": "^0.4.0", "json5": "^2.0.0", "qs": "^6.5.3", "minimatch": "^3.0.5", "semver": "^7.5.3"}, "nyc": {"lines": 95, "statements": 95, "functions": 95, "branches": 80, "include": ["lib/**.js", "lib/**/*.js"], "all": true, "exclude-after-remap": false}, "prettier": {"printWidth": 100, "proseWrap": "always", "singleQuote": true, "tabWidth": 2, "trailingComma": "all", "overrides": [{"files": "*.json", "options": {"tabWidth": 2}}]}, "lint-staged": {"*.{js,ts}": ["eslint --fix"], "*.{md,json}": ["prettier --write"]}, "ava": {"typescript": {"rewritePaths": {"src/": "lib/"}, "compile": false}, "files": ["src/**/*.test.ts"]}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}